# Facebook 工具部署指南

## 📋 系統要求

### 🖥️ 操作系統
- Windows 10/11 或 Linux 或 macOS
- 支援 64 位元系統

### 🐍 Python 環境
- **Python 3.7 或更高版本**（建議 Python 3.8+）
- pip（Python 包管理器）

### 🌐 瀏覽器要求
- **Google Chrome 瀏覽器**（必須安裝）
- Chrome 需要能夠訪問 Facebook

## 📁 必需的項目文件

將以下文件複製到新電腦的同一個資料夾中：

```
facebook_tool/
├── app.py                              # 主要的Flask服務器
├── facebook_deleter_with_urls.py       # 貼文刪除功能
├── facebook_groups_crawler.py          # 社團抓取功能
├── requirements.txt                    # Python依賴列表
├── start_server.bat                    # Windows啟動腳本
├── start_server.sh                     # Linux/Mac啟動腳本
├── templates/
│   └── index.html                      # 前端界面
└── static/                             # 靜態文件目錄（可選）
```

## 🚀 快速部署步驟

### Windows 用戶

1. **安裝 Python**
   - 下載並安裝 Python 3.8+ 從 https://python.org
   - 安裝時勾選 "Add Python to PATH"

2. **複製項目文件**
   - 將所有項目文件複製到新電腦的資料夾中

3. **安裝 Chrome 瀏覽器**
   - 下載並安裝 Google Chrome

4. **運行工具（多種方式）**

   **方法1 - 最簡單**：雙擊 `start.bat`

   **方法2 - PowerShell**：右鍵點擊 `start.ps1` → "使用PowerShell運行"

   **方法3 - Python啟動器**：雙擊 `run.py`

   **方法4 - 手動命令行**：
   ```cmd
   pip install Flask Flask-CORS DrissionPage
   python app.py
   ```

   **方法5 - 如果python不工作，嘗試py**：
   ```cmd
   py -m pip install Flask Flask-CORS DrissionPage
   py app.py
   ```

### Linux/Mac 用戶

1. **安裝 Python**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install python3 python3-pip
   
   # macOS (使用 Homebrew)
   brew install python3
   ```

2. **複製項目文件**
   - 將所有項目文件複製到新電腦的資料夾中

3. **安裝 Chrome 瀏覽器**
   - Linux: 從官網下載 .deb 或 .rpm 包
   - macOS: 從官網下載 .dmg 文件

4. **設置執行權限並運行**
   ```bash
   cd /path/to/facebook_tool
   chmod +x start_server.sh
   chmod +x start_simple.sh
   ./start_server.sh
   ```

   如果遇到問題，嘗試簡化版本：
   ```bash
   ./start_simple.sh
   ```

## 📦 手動安裝依賴（如果自動安裝失敗）

```bash
# 安裝依賴
pip install -r requirements.txt

# 或者逐個安裝
pip install Flask>=2.3.3
pip install Flask-CORS>=4.0.0
pip install DrissionPage>=*******
```

## 🔧 使用步驟

1. **啟動服務器**
   - 運行啟動腳本後，在瀏覽器中訪問 `http://localhost:5000`

2. **準備 Facebook**
   - 打開 Chrome 瀏覽器
   - 登入 Facebook 帳號
   - 保持瀏覽器開啟

3. **使用工具**
   - 在網頁界面中點擊「抓取社團列表」
   - 選擇要處理的社團
   - 選擇刪除模式並執行

## ⚠️ 重要注意事項

1. **Chrome 瀏覽器必須開啟**
   - 工具需要連接到已開啟的 Chrome 瀏覽器
   - 確保 Facebook 已登入

2. **網路連接**
   - 確保網路連接穩定
   - 能夠正常訪問 Facebook

3. **防毒軟體**
   - 某些防毒軟體可能會阻擋瀏覽器自動化
   - 如有問題，請將工具加入白名單

## 🐛 常見問題排除

### Python 相關
- **錯誤：'python' 不是內部或外部命令**
  - 解決：重新安裝 Python 並勾選 "Add to PATH"

### 依賴安裝問題
- **pip 安裝失敗**
  - 解決：升級 pip：`python -m pip install --upgrade pip`

### 瀏覽器連接問題
- **無法連接到 Chrome**
  - 解決：確保 Chrome 已開啟並登入 Facebook
  - 嘗試重啟 Chrome 瀏覽器

### 端口占用
### 啟動腳本問題
- **start_server.bat 無法運行**
  - 解決方案1：使用 `start_simple.bat`
  - 解決方案2：手動執行命令：
    ```cmd
    pip install Flask Flask-CORS DrissionPage
    python app.py
    ```
  - 解決方案3：檢查是否在正確目錄中運行

### 編碼問題（Windows）
- **顯示亂碼或特殊字符錯誤**
  - 解決：使用 `start_simple.bat`（不包含特殊字符）
  - 或在命令提示字元中執行：`chcp 65001`

### 端口占用
- **端口 5000 被占用**
  - 解決：關閉其他使用 5000 端口的程序
  - 或修改 app.py 中的端口號

## 📞 技術支援

如果遇到問題，請檢查：
1. Python 版本是否正確
2. 所有依賴是否安裝成功
3. Chrome 瀏覽器是否正常運行
4. Facebook 是否已登入
5. 網路連接是否正常
