@echo off
echo Starting Facebook Auto...
echo Installing dependencies...
pip install Flask Flask-CORS DrissionPage supabase python-dotenv

echo Starting server...
echo Opening browser in 3 seconds...

REM Start server in background and open browser
start /b python app.py
timeout /t 3 /nobreak >nul
start http://localhost:5000

echo Server is running. Press any key to stop...
pause >nul

REM Kill Python processes
taskkill /f /im python.exe >nul 2>&1
