@echo off
echo Cleaning development environment...

REM Stop Python processes
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1

REM Delete user data files
if exist "urls.txt" del /f /q "urls.txt"
if exist "crawl_info.json" del /f /q "crawl_info.json"
if exist "facebook_deleter.log" del /f /q "facebook_deleter.log"

REM Clean temp files
if exist "%TEMP%\urls.txt" del /f /q "%TEMP%\urls.txt"
if exist "%TEMP%\crawl_info.json" del /f /q "%TEMP%\crawl_info.json"
if exist "%TEMP%\facebook_deleter.log" del /f /q "%TEMP%\facebook_deleter.log"

REM Clean cache
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

echo Clean completed! Please restart the app.
pause
echo Cleaning development environment...

REM Stop Python processes
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1

REM Delete user data files
if exist "urls.txt" del /f /q "urls.txt"
if exist "crawl_info.json" del /f /q "crawl_info.json"
if exist "facebook_deleter.log" del /f /q "facebook_deleter.log"

REM Clean temp files
if exist "%TEMP%\urls.txt" del /f /q "%TEMP%\urls.txt"
if exist "%TEMP%\crawl_info.json" del /f /q "%TEMP%\crawl_info.json"
if exist "%TEMP%\facebook_deleter.log" del /f /q "%TEMP%\facebook_deleter.log"

REM Clean cache
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

echo Clean completed! Please restart the app.
pause
