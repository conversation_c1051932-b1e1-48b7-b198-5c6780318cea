#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試所有模塊的導入是否正常
"""

def test_imports():
    """測試所有關鍵模塊的導入"""
    print("測試模塊導入...")
    print("=" * 50)
    
    try:
        # 測試主要模塊
        print("✅ 測試 facebook_groups_crawler...")
        from facebook_groups_crawler import FacebookGroupsCrawler
        crawler = FacebookGroupsCrawler()
        print("   - FacebookGroupsCrawler 導入成功")
        
        print("✅ 測試 facebook_deleter_with_urls...")
        from facebook_deleter_with_urls import FacebookDrissionDeleter
        deleter = FacebookDrissionDeleter()
        print("   - FacebookDrissionDeleter 導入成功")
        
        print("✅ 測試 auth_system...")
        from auth.auth_system import auth_system
        device_id = auth_system.get_device_id()
        print(f"   - auth_system 導入成功，設備ID: {device_id}")
        
        print("\n🎉 所有模塊導入測試通過！")
        print("現在可以安全打包，不會出現 'name os is not defined' 錯誤。")
        
        return True
        
    except Exception as e:
        print(f"❌ 導入測試失敗: {e}")
        return False

if __name__ == "__main__":
    test_imports()
