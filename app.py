#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Facebook Auto API服務器
"""

from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import threading
import time
import logging
import json
import os
from typing import Dict, Optional

# 導入現有的功能類
from facebook_deleter_with_urls import FacebookDrissionDeleter
from facebook_groups_crawler import FacebookGroupsCrawler
from auth.auth_system import auth_system

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 屏蔽 Flask 的訪問日誌（只顯示錯誤和警告）
logging.getLogger('werkzeug').setLevel(logging.WARNING)

# 如果想要完全屏蔽所有 HTTP 請求日誌，可以取消下面這行的註釋
# logging.getLogger('werkzeug').disabled = True

# 創建Flask應用
app = Flask(__name__)
CORS(app)  # 允許跨域請求

# 全局變量來存儲任務狀態
task_status = {
    "crawler": {
        "running": False,
        "progress": 0,
        "message": "",
        "result": None,
        "error": None
    },
    "deleter": {
        "running": False,
        "progress": 0,
        "message": "",
        "result": None,
        "error": None,
        "target_tags": [],
        "delete_all": False
    }
}

# 全局變量來存儲社團列表
groups_data = {
    "user_id": None,
    "groups": [],
    "selected_groups": []
}

def update_crawler_progress(progress: int, message: str):
    """更新爬蟲進度回調函數"""
    task_status["crawler"]["progress"] = progress
    task_status["crawler"]["message"] = message
    logger.info(f"爬蟲進度 {progress}%: {message}")

def update_deleter_progress(progress: int, message: str):
    """更新刪除器進度回調函數"""
    task_status["deleter"]["progress"] = progress
    task_status["deleter"]["message"] = message
    logger.info(f"刪除器進度 {progress}%: {message}")

@app.route('/')
def index():
    """主頁面"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """獲取所有任務的狀態"""
    return jsonify(task_status)

@app.route('/api/crawler/start', methods=['POST'])
def start_crawler():
    """啟動社團抓取任務"""
    if task_status["crawler"]["running"]:
        return jsonify({"error": "爬蟲任務已在運行中"}), 400

    # 檢查設備授權
    try:
        from DrissionPage import ChromiumPage
        page = ChromiumPage()
        cookies = page.cookies()
        user_id = None

        for cookie in cookies:
            if cookie.get('name') == 'c_user':
                user_id = cookie.get('value')
                break

        if user_id:
            device_id = auth_system.get_device_id()
            auth_result = auth_system.verify_authorization(user_id, device_id)

            if not auth_result["authorized"]:
                return jsonify({
                    "error": f"設備未授權: {auth_result['message']}",
                    "auth_required": True
                }), 403

            # 檢查權限
            permissions = auth_result.get("permissions", [])
            if "crawl" not in permissions:
                return jsonify({
                    "error": "沒有抓取權限",
                    "auth_required": True
                }), 403
        else:
            return jsonify({"error": "請先登入Facebook"}), 401

    except Exception as auth_error:
        return jsonify({"error": f"授權檢查失敗: {str(auth_error)}"}), 500

    try:
        # 重置狀態
        task_status["crawler"]["running"] = True
        task_status["crawler"]["progress"] = 0
        task_status["crawler"]["message"] = "準備開始抓取..."
        task_status["crawler"]["result"] = None
        task_status["crawler"]["error"] = None
        
        def crawler_task():
            try:
                crawler = FacebookGroupsCrawler(
                    headless=False,
                    progress_callback=update_crawler_progress
                )
                
                result = crawler.crawl_all_groups(
                    use_existing_browser=True,
                    save_to_file=True
                )
                
                if result["success"]:
                    task_status["crawler"]["result"] = result
                    task_status["crawler"]["message"] = "抓取完成！"
                    task_status["crawler"]["progress"] = 100

                    # 保存社團數據到全局變量（即使文件保存失敗也要保存到內存）
                    try:
                        groups_data["user_id"] = result["user_id"]
                        groups_data["groups"] = result.get("groups", [])
                        groups_data["selected_groups"] = []  # 重置選擇

                        logger.info(f"✅ 已保存 {len(groups_data['groups'])} 個社團到內存")
                        logger.info(f"用戶ID: {groups_data['user_id']}")

                        # 調試：輸出前幾個社團信息
                        if groups_data["groups"]:
                            for i, group in enumerate(groups_data["groups"][:3]):
                                logger.info(f"社團 {i+1}: {group.get('name', '無名稱')} - {group.get('id', '無ID')}")

                    except Exception as save_error:
                        logger.error(f"保存社團數據到內存失敗: {save_error}")
                        # 即使保存失敗，也嘗試保存基本信息
                        groups_data["user_id"] = result.get("user_id")
                        groups_data["groups"] = []
                        groups_data["selected_groups"] = []
                else:
                    task_status["crawler"]["error"] = result["message"]
                    task_status["crawler"]["message"] = f"抓取失敗: {result['message']}"
                    
            except Exception as e:
                error_msg = f"抓取過程中發生錯誤: {str(e)}"
                task_status["crawler"]["error"] = error_msg
                task_status["crawler"]["message"] = error_msg
                logger.error(error_msg)
            finally:
                task_status["crawler"]["running"] = False
        
        # 在後台線程中運行抓取任務
        thread = threading.Thread(target=crawler_task)
        thread.daemon = True
        thread.start()
        
        return jsonify({"message": "社團抓取任務已開始"})
        
    except Exception as e:
        task_status["crawler"]["running"] = False
        task_status["crawler"]["error"] = str(e)
        return jsonify({"error": f"啟動抓取任務失敗: {str(e)}"}), 500

@app.route('/api/deleter/start', methods=['POST'])
def start_deleter():
    """啟動貼文刪除任務"""
    if task_status["deleter"]["running"]:
        return jsonify({"error": "刪除任務已在運行中"}), 400

    # 檢查設備授權
    try:
        from DrissionPage import ChromiumPage
        page = ChromiumPage()
        cookies = page.cookies()
        user_id = None

        for cookie in cookies:
            if cookie.get('name') == 'c_user':
                user_id = cookie.get('value')
                break

        if user_id:
            device_id = auth_system.get_device_id()
            auth_result = auth_system.verify_authorization(user_id, device_id)

            if not auth_result["authorized"]:
                return jsonify({
                    "error": f"設備未授權: {auth_result['message']}",
                    "auth_required": True
                }), 403

            # 檢查權限
            permissions = auth_result.get("permissions", [])
            if "delete" not in permissions:
                return jsonify({
                    "error": "沒有刪除權限",
                    "auth_required": True
                }), 403
        else:
            return jsonify({"error": "請先登入Facebook"}), 401

    except Exception as auth_error:
        return jsonify({"error": f"授權檢查失敗: {str(auth_error)}"}), 500

    try:
        data = request.get_json()
        target_tags = data.get('target_tags', [])
        delete_all = data.get('delete_all', False)

        # 檢查是否有選中的社團
        if not groups_data["selected_groups"]:
            return jsonify({"error": "請先抓取社團並選擇要刪除貼文的社團"}), 400

        if not groups_data["user_id"]:
            return jsonify({"error": "未找到用戶ID，請重新抓取社團"}), 400
        
        # 重置狀態
        task_status["deleter"]["running"] = True
        task_status["deleter"]["progress"] = 0
        task_status["deleter"]["message"] = "準備開始刪除..."
        task_status["deleter"]["result"] = None
        task_status["deleter"]["error"] = None
        task_status["deleter"]["target_tags"] = target_tags
        task_status["deleter"]["delete_all"] = delete_all
        
        def deleter_task():
            try:
                deleter = FacebookDrissionDeleter(headless=False)
                
                update_deleter_progress(10, "設置瀏覽器...")
                if not deleter.setup_browser(use_existing_browser=True):
                    raise Exception("瀏覽器設置失敗")
                
                update_deleter_progress(20, "檢查Facebook登入狀態...")
                if not deleter.check_facebook_login_status():
                    raise Exception("Facebook未登入")
                
                update_deleter_progress(30, "生成選中社團的用戶動態頁面URL...")

                # 根據選中的社團生成用戶動態頁面URL
                selected_group_ids = groups_data["selected_groups"]
                user_id = groups_data["user_id"]

                group_user_urls = []
                for group_id in selected_group_ids:
                    user_profile_url = f"https://www.facebook.com/groups/{group_id}/user/{user_id}"
                    group_user_urls.append(user_profile_url)

                if not group_user_urls:
                    raise Exception("沒有選中任何社團")
                
                update_deleter_progress(40, f"開始{'全部' if delete_all else '針對性'}刪除...")
                
                if delete_all:
                    # 全部刪除模式
                    result = deleter.delete_all_posts(group_user_urls, update_deleter_progress)
                else:
                    # 針對性刪除模式
                    if not target_tags:
                        raise Exception("針對性刪除需要指定標籤")
                    result = deleter.delete_posts_with_tags(group_user_urls, target_tags, update_deleter_progress)
                
                if result["success"]:
                    task_status["deleter"]["result"] = result
                    task_status["deleter"]["message"] = "刪除完成！"
                    task_status["deleter"]["progress"] = 100
                else:
                    task_status["deleter"]["error"] = result["message"]
                    task_status["deleter"]["message"] = f"刪除失敗: {result['message']}"
                    
            except Exception as e:
                error_msg = f"刪除過程中發生錯誤: {str(e)}"
                task_status["deleter"]["error"] = error_msg
                task_status["deleter"]["message"] = error_msg
                logger.error(error_msg)
            finally:
                task_status["deleter"]["running"] = False
        
        # 在後台線程中運行刪除任務
        thread = threading.Thread(target=deleter_task)
        thread.daemon = True
        thread.start()
        
        return jsonify({"message": f"{'全部' if delete_all else '針對性'}刪除任務已開始"})
        
    except Exception as e:
        task_status["deleter"]["running"] = False
        task_status["deleter"]["error"] = str(e)
        return jsonify({"error": f"啟動刪除任務失敗: {str(e)}"}), 500

@app.route('/api/deleter/stop', methods=['POST'])
def stop_deleter():
    """停止刪除任務（注意：只能標記為停止，實際停止需要手動中斷）"""
    task_status["deleter"]["running"] = False
    task_status["deleter"]["message"] = "用戶請求停止任務"
    return jsonify({"message": "已請求停止刪除任務"})

@app.route('/api/crawler/stop', methods=['POST'])
def stop_crawler():
    """停止抓取任務（注意：只能標記為停止，實際停止需要手動中斷）"""
    task_status["crawler"]["running"] = False
    task_status["crawler"]["message"] = "用戶請求停止任務"
    return jsonify({"message": "已請求停止抓取任務"})

@app.route('/api/groups')
def get_groups():
    """獲取社團列表"""
    try:
        # 如果內存中沒有數據，嘗試從文件載入
        if not groups_data["groups"] and os.path.exists('crawl_info.json'):
            logger.info("📂 內存中沒有社團數據，嘗試從文件載入...")
            try:
                with open('crawl_info.json', 'r', encoding='utf-8') as f:
                    file_data = json.load(f)

                groups_data["user_id"] = file_data.get("user_id")
                groups_data["groups"] = file_data.get("groups", [])
                groups_data["selected_groups"] = []  # 重置選擇

                logger.info(f"✅ 從文件載入了 {len(groups_data['groups'])} 個社團")
                logger.info(f"用戶ID: {groups_data['user_id']}")

            except Exception as load_error:
                logger.error(f"從文件載入社團數據失敗: {load_error}")

        logger.info(f"📋 API請求社團列表，當前數據：用戶ID={groups_data['user_id']}, 社團數量={len(groups_data['groups'])}")

        return jsonify({
            "user_id": groups_data["user_id"],
            "groups": groups_data["groups"],
            "selected_groups": groups_data["selected_groups"],
            "total_groups": len(groups_data["groups"])
        })
    except Exception as e:
        logger.error(f"獲取社團列表失敗: {e}")
        return jsonify({
            "user_id": None,
            "groups": [],
            "selected_groups": [],
            "total_groups": 0,
            "error": str(e)
        })

@app.route('/api/groups/select', methods=['POST'])
def select_groups():
    """設定選中的社團"""
    try:
        data = request.get_json()
        selected_group_ids = data.get('selected_group_ids', [])

        # 驗證選中的社團ID是否有效
        valid_group_ids = [group['id'] for group in groups_data["groups"]]
        invalid_ids = [gid for gid in selected_group_ids if gid not in valid_group_ids]

        if invalid_ids:
            return jsonify({"error": f"無效的社團ID: {invalid_ids}"}), 400

        groups_data["selected_groups"] = selected_group_ids

        return jsonify({
            "message": f"已選擇 {len(selected_group_ids)} 個社團",
            "selected_groups": selected_group_ids
        })

    except Exception as e:
        return jsonify({"error": f"設定選中社團失敗: {str(e)}"}), 500

@app.route('/api/browser/check', methods=['POST'])
def check_browser():
    """檢查瀏覽器連接和Facebook登入狀態"""
    try:
        from DrissionPage import ChromiumPage
        from DrissionPage.common import Settings

        # 嘗試連接到現有瀏覽器
        try:
            # 設置為連接模式，不創建新瀏覽器
            Settings.singleton_tab_obj = True

            # 嘗試連接到現有的瀏覽器實例
            page = ChromiumPage()

            # 檢查是否能獲取當前URL
            current_url = page.url

            # 檢查是否在Facebook域名
            is_facebook = 'facebook.com' in current_url.lower()

            # 嘗試獲取用戶ID（檢查是否已登入）
            user_id = None
            login_status = False

            try:
                cookies = page.cookies()
                for cookie in cookies:
                    if cookie.get('name') == 'c_user':
                        user_id = cookie.get('value')
                        login_status = True
                        break
            except:
                pass

            # 獲取頁面標題
            try:
                page_title = page.title
            except:
                page_title = "無法獲取頁面標題"

            # 如果未登入Facebook，自動導航到Facebook登入頁面
            if not login_status and not is_facebook:
                try:
                    page.get('https://www.facebook.com')
                    page_title = "已自動導航到Facebook登入頁面"
                    current_url = page.url
                    is_facebook = True
                except:
                    pass

            # 不關閉瀏覽器，讓用戶可以使用它登入
            # page.quit() # 保持瀏覽器開啟

            # 設備授權檢查 - 防濫用機制
            device_id = auth_system.get_device_id()
            auth_result = {"authorized": False}  # 默認不授權
            auto_bind_result = None

            if login_status and user_id:
                # 檢查設備綁定狀態
                auth_result = auth_system.verify_authorization(user_id, device_id)

                if not auth_result["authorized"]:
                    # 檢查是否是設備綁定到不同用戶的情況
                    if auth_result.get("reason") == "device_bound_to_different_user":
                        # 設備已綁定到其他Facebook ID，拒絕訪問
                        pass  # 保持未授權狀態
                    elif auth_system.is_auto_bind_enabled():
                        # 設備未綁定，嘗試自動綁定
                        auto_bind_result = auth_system.auto_bind_device(user_id, device_id)

                        if auto_bind_result["success"]:
                            # 自動綁定成功，重新驗證授權
                            auth_result = auth_system.verify_authorization(user_id, device_id)

            # 準備返回信息
            response_data = {
                "success": True,
                "browser_connected": True,
                "current_url": current_url,
                "is_facebook": is_facebook,
                "login_status": login_status,
                "user_id": user_id,
                "page_title": page_title,
                "device_id": device_id,
                "authorized": auth_result["authorized"],
                "auth_message": auth_result.get("message", ""),
                "permissions": auth_result.get("permissions", []),
                "auto_bind_enabled": auth_system.is_auto_bind_enabled()
            }

            # 添加自動綁定信息
            if auto_bind_result:
                response_data["auto_bind"] = auto_bind_result
                if auto_bind_result["success"]:
                    if auto_bind_result["existing"]:
                        response_data["message"] = "設備已自動綁定（已存在）"
                    else:
                        response_data["message"] = "設備已自動綁定成功"
                else:
                    response_data["message"] = f"自動綁定失敗: {auto_bind_result['message']}"
            else:
                response_data["message"] = "瀏覽器連接成功" if login_status and auth_result["authorized"] else "需要授權或登入"

            return jsonify(response_data)

        except Exception as browser_error:
            return jsonify({
                "success": False,
                "browser_connected": False,
                "error": f"無法連接到瀏覽器: {str(browser_error)}",
                "message": "請確保Chrome瀏覽器已開啟"
            })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"檢查失敗: {str(e)}"
        }), 500

# 文件下載功能已移除

@app.route('/api/logs/download')
def download_logs():
    """下載日誌文件"""
    if os.path.exists('facebook_deleter.log'):
        return send_from_directory('.', 'facebook_deleter.log', as_attachment=True)
    else:
        return jsonify({"error": "日誌文件不存在"}), 404

@app.route('/api/files/status')
def get_files_status():
    """獲取文件狀態"""
    files_status = {
        "urls_txt": os.path.exists('urls.txt'),
        "crawl_info_json": os.path.exists('crawl_info.json'),
        "log_file": os.path.exists('facebook_deleter.log')
    }
    
    # 如果urls.txt存在，讀取行數
    if files_status["urls_txt"]:
        try:
            with open('urls.txt', 'r', encoding='utf-8') as f:
                files_status["urls_count"] = len([line for line in f if line.strip()])
        except:
            files_status["urls_count"] = 0
    else:
        files_status["urls_count"] = 0
    
    return jsonify(files_status)

@app.route('/api/clear', methods=['POST'])
def clear_data():
    """清理所有用戶數據"""
    try:
        global groups_data, task_status

        # 清理全局變量
        groups_data = {
            "user_id": None,
            "groups": [],
            "selected_groups": []
        }

        # 重置任務狀態
        task_status = {
            "crawler": {
                "running": False,
                "progress": 0,
                "message": "",
                "result": None,
                "error": None
            },
            "deleter": {
                "running": False,
                "progress": 0,
                "message": "",
                "result": None,
                "error": None,
                "target_tags": [],
                "delete_all": False
            }
        }

        logger.info("✅ 已清理所有用戶數據")

        return jsonify({
            "success": True,
            "message": "所有用戶數據已清理"
        })

    except Exception as e:
        logger.error(f"清理數據失敗: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/auth/register', methods=['POST'])
def register_device():
    """註冊設備授權 - 自動抓取Facebook ID並綁定"""
    try:
        data = request.get_json()
        auth_code = data.get('auth_code')
        device_name = data.get('device_name')

        if not auth_code:
            return jsonify({
                "success": False,
                "message": "請輸入授權碼"
            }), 400

        # 自動抓取當前登入的Facebook ID
        try:
            from DrissionPage import ChromiumPage
            page = ChromiumPage()
            cookies = page.cookies()
            facebook_user_id = None

            for cookie in cookies:
                if cookie.get('name') == 'c_user':
                    facebook_user_id = cookie.get('value')
                    break

            if not facebook_user_id:
                return jsonify({
                    "success": False,
                    "message": "請先登入Facebook，系統需要抓取您的Facebook ID進行綁定"
                }), 400

        except Exception as browser_error:
            return jsonify({
                "success": False,
                "message": f"無法抓取Facebook ID: {str(browser_error)}"
            }), 500

        # 使用抓取到的Facebook ID註冊設備
        result = auth_system.register_device_with_code(auth_code, facebook_user_id, device_name)

        if result["success"]:
            # 記錄綁定成功
            auth_system.log_usage(facebook_user_id, auth_system.get_device_id(), "facebook_id_bound")

        return jsonify(result)

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"註冊失敗: {str(e)}"
        }), 500

if __name__ == '__main__':
    # 確保templates目錄存在
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    # 確保static目錄存在
    if not os.path.exists('static'):
        os.makedirs('static')
    
    print("🚀 Facebook Auto 服務器啟動中...")
    print("🌐 請在瀏覽器中訪問: http://localhost:5000")
    print("📋 API文檔:")
    print("   GET  /api/status - 獲取任務狀態")
    print("   POST /api/browser/check - 檢查瀏覽器狀態")
    print("   POST /api/auth/register - 註冊設備授權")
    print("   POST /api/crawler/start - 開始抓取社團")
    print("   GET  /api/groups - 獲取社團列表")
    print("   POST /api/groups/select - 設定選中的社團")
    print("   POST /api/deleter/start - 開始刪除貼文")
    print("💡 提示：HTTP 訪問日誌已屏蔽，只顯示重要信息")
    print()

    # 檢查是否為開發模式
    debug_mode = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

    if debug_mode:
        print("🔧 開發模式：顯示詳細日誌")
        logging.getLogger('werkzeug').setLevel(logging.INFO)
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("🚀 生產模式：屏蔽訪問日誌")
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)