@echo off
echo Facebook Tool - Quick Start
echo ==============================

echo Select what you want to do:
echo.
echo 1. Start Facebook Tool (Main App)
echo 2. Manage Auth System
echo 3. Build Tools (All build options)
echo 4. Clean Development Environment
echo 5. Exit

set /p choice="Please choose (1-5): "

if "%choice%"=="1" (
    echo Starting Facebook Tool...
    python app.py
    goto end
)

if "%choice%"=="2" (
    echo Starting Auth Management...
    cd auth
    call run_auth_tools.bat
    cd ..
    goto end
)

if "%choice%"=="3" (
    echo Starting Build Tools...
    cd scripts
    call build_tools.bat
    cd ..
    goto end
)

if "%choice%"=="4" (
    echo Cleaning Development Environment...
    call scripts\clean_dev.bat
    goto end
)

if "%choice%"=="5" (
    echo Goodbye!
    goto end
)

echo Invalid choice, please try again.
pause
goto start

:end
pause
