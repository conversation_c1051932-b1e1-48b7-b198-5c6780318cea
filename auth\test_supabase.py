try:
    from .auth_system import auth_system
except ImportError:
    from auth_system import auth_system

def test_connection():
    """測試 Supabase 連接"""
    try:
        # 測試獲取設備ID
        device_id = auth_system.get_device_id()
        print(f"✅ 設備ID: {device_id}")
        
        # 測試數據庫連接
        result = auth_system.supabase.table('auth_codes').select('count').execute()
        print(f"✅ Supabase 連接成功")
        
        # 測試授權驗證（應該返回未授權）
        auth_result = auth_system.verify_authorization("test_user", device_id)
        print(f"✅ 授權驗證測試: {auth_result['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("測試 Supabase 連接...")
    print("=" * 40)
    
    if test_connection():
        print("\n🎉 所有測試通過！")
        print("\n下一步:")
        print("1. 在 Supabase 中創建數據表")
        print("2. 運行 create_auth_code.py 創建授權碼")
        print("3. 集成到主應用程序")
    else:
        print("\n❌ 測試失敗，請檢查配置")
