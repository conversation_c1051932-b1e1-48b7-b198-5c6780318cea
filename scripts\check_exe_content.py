#!/usr/bin/env python3
"""
檢查 exe 文件中是否包含用戶數據
"""

import sys
import re

def check_exe_for_user_data(exe_path):
    """檢查 exe 文件中是否包含用戶數據"""
    print(f"檢查文件: {exe_path}")
    
    try:
        with open(exe_path, 'rb') as f:
            content = f.read()
        
        # 轉換為字符串進行搜索
        try:
            text_content = content.decode('utf-8', errors='ignore')
        except:
            text_content = content.decode('latin-1', errors='ignore')
        
        # 搜索用戶ID模式
        user_id_pattern = r'100000\d{9}'
        matches = re.findall(user_id_pattern, text_content)
        
        if matches:
            print(f"❌ 發現用戶ID: {set(matches)}")
            return False
        
        # 搜索可疑的URL模式
        url_patterns = [
            r'facebook\.com/groups/[^/]+/user/\d+',
            r'groups/[^/]+/user/\d+',
        ]
        
        found_urls = False
        for pattern in url_patterns:
            url_matches = re.findall(pattern, text_content)
            if url_matches:
                print(f"❌ 發現可疑URL: {url_matches[:3]}...")  # 只顯示前3個
                found_urls = True
        
        if found_urls:
            return False
        
        # 搜索文件名模式
        file_patterns = [
            r'urls\.txt',
            r'crawl_info\.json',
            r'facebook_deleter\.log'
        ]
        
        found_files = False
        for pattern in file_patterns:
            if re.search(pattern, text_content):
                print(f"⚠️ 發現文件名引用: {pattern}")
                found_files = True
        
        if not found_files:
            print("✅ 未發現用戶數據")
            return True
        else:
            print("⚠️ 發現文件名引用，但可能是代碼中的正常引用")
            return True
            
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        return False

def main():
    if len(sys.argv) != 2:
        print("使用方法: python check_exe_content.py <exe文件路徑>")
        print("例如: python check_exe_content.py FacebookTool.exe")
        return
    
    exe_path = sys.argv[1]
    
    print("=" * 50)
    print("EXE 文件用戶數據檢查工具")
    print("=" * 50)
    
    is_clean = check_exe_for_user_data(exe_path)
    
    print("\n" + "=" * 50)
    if is_clean:
        print("✅ 檢查通過：文件安全，可以分發")
    else:
        print("❌ 檢查失敗：文件包含用戶數據，不可分發")
    print("=" * 50)

if __name__ == "__main__":
    main()
