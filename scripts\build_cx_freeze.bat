@echo off
echo Building Facebook Tool with cx_Freeze...
echo =========================================

echo Step 1: Clean environment
call clean_dev.bat

echo Step 2: Install cx_Freeze
pip install cx_Freeze

echo Step 3: Build with cx_Freeze
cd ..
python scripts\setup.py build
cd scripts

echo Step 4: Check build result
if exist "..\build\exe.win-amd64-*\FacebookTool.exe" (
    echo ✅ cx_Freeze build successful!
    
    echo Step 5: Find exact build path
    for /d %%i in ("..\build\exe.win-amd64-*") do (
        set "BUILD_PATH=%%i"
        echo Found build: %%i
    )
    
    echo Step 6: Security check
    python check_exe_content.py "%BUILD_PATH%\FacebookTool.exe"
    
    echo Step 7: Copy to release
    if not exist "..\release" mkdir "..\release"
    copy "%BUILD_PATH%\FacebookTool.exe" "..\release\FacebookTool_CxFreeze.exe"
    
    echo ✅ cx_Freeze build completed!
    echo Executable: release\FacebookTool_CxFreeze.exe
) else (
    echo ❌ cx_Freeze build failed!
)

pause
