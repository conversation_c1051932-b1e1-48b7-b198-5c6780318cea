#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 用於打包後的配置管理
"""

import os

class Config:
    """配置管理類"""
    
    # Supabase 配置
    SUPABASE_URL = "https://clyhepqziqkzufzeqrsc.supabase.co"
    SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNseWhlcHF6aXFrenVmemVxcnNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwMzk2NTIsImV4cCI6MjA2OTYxNTY1Mn0.10wSOqHOOS8tyYEwF3oUzVZRc3voA9f-KPNcDvyQZLo"
    
    # 自動綁定配置
    AUTO_BIND_ENABLED = False
    
    @classmethod
    def get_supabase_url(cls):
        """獲取Supabase URL"""
        return os.environ.get("SUPABASE_URL", cls.SUPABASE_URL)
    
    @classmethod
    def get_supabase_key(cls):
        """獲取Supabase Key"""
        return os.environ.get("SUPABASE_KEY", cls.SUPABASE_KEY)
    
    @classmethod
    def is_auto_bind_enabled(cls):
        """檢查是否啟用自動綁定"""
        env_value = os.environ.get("AUTO_BIND_ENABLED", str(cls.AUTO_BIND_ENABLED))
        return env_value.lower() in ('true', '1', 'yes', 'on')
