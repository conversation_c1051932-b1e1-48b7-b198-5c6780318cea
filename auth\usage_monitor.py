try:
    from .auth_system import auth_system
except ImportError:
    from auth_system import auth_system
from datetime import datetime, <PERSON><PERSON><PERSON>

def monitor_usage():
    """監控工具使用情況"""
    try:
        # 獲取最近24小時的使用日誌
        yesterday = datetime.now() - timedelta(days=1)
        
        result = auth_system.supabase.table('usage_logs').select('*').gte(
            'timestamp', yesterday.isoformat()
        ).order('timestamp', desc=True).execute()
        
        print(f"\n最近24小時使用記錄 ({len(result.data)} 條):")
        print("=" * 100)
        print(f"{'時間':<20} {'Facebook ID':<20} {'設備ID':<20} {'操作':<20}")
        print("-" * 100)
        
        for log in result.data:
            timestamp = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00')).strftime('%m-%d %H:%M:%S')
            print(f"{timestamp:<20} {log['user_id']:<20} {log['device_id']:<20} {log['action']:<20}")
        
        print("-" * 100)
        
        # 統計信息
        users = set(log['user_id'] for log in result.data)
        devices = set(log['device_id'] for log in result.data)
        
        print(f"\n統計信息:")
        print(f"活躍用戶數: {len(users)}")
        print(f"活躍設備數: {len(devices)}")
        
        # 檢查異常使用
        print(f"\n異常檢測:")
        
        # 檢查是否有設備嘗試使用多個Facebook ID
        device_users = {}
        for log in result.data:
            device_id = log['device_id']
            user_id = log['user_id']
            
            if device_id not in device_users:
                device_users[device_id] = set()
            device_users[device_id].add(user_id)
        
        suspicious_devices = {k: v for k, v in device_users.items() if len(v) > 1}
        
        if suspicious_devices:
            print("⚠️ 發現可疑設備（多個Facebook ID使用同一設備）:")
            for device_id, user_ids in suspicious_devices.items():
                print(f"  設備 {device_id}: Facebook IDs {list(user_ids)}")
        else:
            print("✅ 未發現異常使用")
            
    except Exception as e:
        print(f"監控失敗: {e}")

def check_unauthorized_attempts():
    """檢查未授權訪問嘗試"""
    try:
        # 查找驗證失敗的日誌
        result = auth_system.supabase.table('usage_logs').select('*').eq(
            'action', 'verify_failed'
        ).order('timestamp', desc=True).limit(50).execute()
        
        if not result.data:
            print("✅ 未發現未授權訪問嘗試")
            return
        
        print(f"\n未授權訪問嘗試 ({len(result.data)} 條):")
        print("=" * 100)
        print(f"{'時間':<20} {'Facebook ID':<20} {'設備ID':<20}")
        print("-" * 100)
        
        for log in result.data:
            timestamp = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00')).strftime('%m-%d %H:%M:%S')
            print(f"{timestamp:<20} {log['user_id']:<20} {log['device_id']:<20}")
        
        print("-" * 100)
        
    except Exception as e:
        print(f"檢查失敗: {e}")

def main():
    print("使用監控工具")
    print("=" * 50)
    print("監控工具使用情況，防止濫用")
    
    while True:
        print(f"\n選擇操作:")
        print("1. 查看使用記錄")
        print("2. 檢查未授權嘗試")
        print("3. 實時監控（每30秒刷新）")
        print("4. 退出")
        
        choice = input("\n請選擇 (1-4): ").strip()
        
        if choice == "1":
            monitor_usage()
            
        elif choice == "2":
            check_unauthorized_attempts()
            
        elif choice == "3":
            print("開始實時監控（按 Ctrl+C 停止）...")
            try:
                import time
                while True:
                    print("\n" + "="*50)
                    print(f"監控時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    monitor_usage()
                    time.sleep(30)
            except KeyboardInterrupt:
                print("\n監控已停止")
            
        elif choice == "4":
            break
            
        else:
            print("無效選擇，請重試")

if __name__ == "__main__":
    main()
