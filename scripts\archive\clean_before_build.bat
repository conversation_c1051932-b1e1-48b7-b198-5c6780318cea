@echo off
chcp 65001 >nul
echo Cleaning user data for safe packaging...
echo.

REM Delete files containing user information
if exist "urls.txt" (
    echo Deleting urls.txt
    del "urls.txt"
)

if exist "crawl_info.json" (
    echo Deleting crawl_info.json
    del "crawl_info.json"
)

if exist "facebook_deleter.log" (
    echo Deleting facebook_deleter.log
    del "facebook_deleter.log"
)

REM Clean cache files
if exist "__pycache__" (
    echo Cleaning __pycache__ directory
    rmdir /s /q "__pycache__"
)

if exist "build" (
    echo Cleaning old build directory
    rmdir /s /q "build"
)

if exist "dist" (
    echo Cleaning old dist directory
    rmdir /s /q "dist"
)

REM Clean any .pyc files
echo Cleaning .pyc files
for /r %%i in (*.pyc) do (
    if exist "%%i" del "%%i"
)

echo.
echo Clean completed! Safe to package now.
echo.
pause
