# 用戶數據文件 - 包含個人隱私信息
urls.txt
crawl_info.json
facebook_deleter.log
*.log

# Python 緩存和編譯文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分發和打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec.bak

# 單元測試
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 環境變數
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE 設定
.vscode/
.idea/
*.swp
*.swo
*~

# 作業系統
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 臨時文件
*.tmp
*.temp
*.bak
*.backup
