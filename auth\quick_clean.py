from auth_system import auth_system

def quick_clean_current_device():
    device_id = auth_system.get_device_id()
    print(f"Cleaning device: {device_id}")
    
    try:
        # Clean device authorizations
        device_result = auth_system.supabase.table('device_authorizations').delete().eq('device_id', device_id).execute()
        print(f"Cleaned device auth records: {len(device_result.data)}")
        
        # Clean usage logs  
        log_result = auth_system.supabase.table('usage_logs').delete().eq('device_id', device_id).execute()
        print(f"Cleaned usage logs: {len(log_result.data)}")
        
        print("SUCCESS: All test data cleaned!")
        print("Safe to package now - no test bindings included.")
        
    except Exception as e:
        print(f"ERROR: {e}")

if __name__ == "__main__":
    quick_clean_current_device()
