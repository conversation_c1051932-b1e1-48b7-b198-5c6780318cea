#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試配置系統是否正常工作
"""

def test_config():
    """測試配置系統"""
    print("測試配置系統...")
    print("=" * 50)
    
    try:
        # 測試導入
        from auth.config import Config
        print("✅ 配置模塊導入成功")
        
        # 測試Supabase配置
        url = Config.get_supabase_url()
        key = Config.get_supabase_key()
        
        print(f"✅ Supabase URL: {url[:30]}...")
        print(f"✅ Supabase Key: {key[:30]}...")
        
        # 測試自動綁定配置
        auto_bind = Config.is_auto_bind_enabled()
        print(f"✅ 自動綁定: {auto_bind}")
        
        # 測試授權系統
        from auth.auth_system import auth_system
        device_id = auth_system.get_device_id()
        print(f"✅ 設備ID: {device_id}")
        
        print("\n🎉 所有配置測試通過！")
        print("現在可以安全打包，不會出現配置錯誤。")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置測試失敗: {e}")
        return False

if __name__ == "__main__":
    test_config()
