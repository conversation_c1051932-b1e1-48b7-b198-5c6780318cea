#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Facebook 社團抓取器 - 後端API版本
自動抓取已加入的社團並生成個人動態頁面URL
"""

import time
import logging
import re
import json
import os
from typing import List, Dict, Optional, Callable
from DrissionPage import ChromiumPage

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FacebookGroupsCrawler:
    """Facebook 社團抓取器 - 後端API版本"""

    def __init__(self, headless: bool = False, progress_callback: Optional[Callable] = None):
        self.page = None
        self.user_id = None
        self.headless = headless
        self.progress_callback = progress_callback
        self.current_progress = 0
        self.current_message = ""

    def update_progress(self, progress: int, message: str):
        """更新進度和狀態"""
        self.current_progress = progress
        self.current_message = message
        logger.info(f"進度 {progress}%: {message}")

        if self.progress_callback:
            self.progress_callback(progress, message)
        
    def setup_browser(self, use_existing_browser: bool = True):
        """設定瀏覽器"""
        try:
            self.update_progress(5, "設定瀏覽器...")

            if use_existing_browser:
                self.page = ChromiumPage()
                self.update_progress(10, "成功連接到現有瀏覽器")
            else:
                self.page = ChromiumPage(headless=self.headless)
                self.update_progress(10, "成功啟動新的瀏覽器")
            return True
        except Exception as e:
            error_msg = f"設定瀏覽器失敗: {e}"
            logger.error(error_msg)
            self.update_progress(0, error_msg)
            return False
    
    def extract_user_id_from_cookies(self) -> Optional[str]:
        """從瀏覽器Cookie中提取c_user ID"""
        try:
            self.update_progress(15, "從Cookie中提取用戶ID...")

            # 獲取所有Cookie
            cookies = self.page.run_js("""
            var cookies = {};
            document.cookie.split(';').forEach(function(cookie) {
                var parts = cookie.trim().split('=');
                if (parts.length === 2) {
                    cookies[parts[0]] = parts[1];
                }
            });
            return cookies;
            """)

            # 提取c_user
            if 'c_user' in cookies:
                user_id = cookies['c_user']
                self.update_progress(20, f"成功提取用戶ID: {user_id}")
                self.user_id = user_id
                return user_id
            else:
                error_msg = "未找到c_user Cookie，請確保已登入Facebook"
                logger.error(error_msg)
                self.update_progress(0, error_msg)
                return None

        except Exception as e:
            error_msg = f"提取用戶ID失敗: {e}"
            logger.error(error_msg)
            self.update_progress(0, error_msg)
            return None
    
    def navigate_to_groups_page(self) -> bool:
        """導航到社團頁面"""
        try:
            self.update_progress(25, "導航到Facebook社團頁面...")
            self.page.get("https://www.facebook.com/groups/joins/")
            time.sleep(5)

            # 檢查是否成功到達社團頁面
            current_url = self.page.url
            if "facebook.com/groups" in current_url:
                self.update_progress(30, "成功到達社團頁面")
                return True
            else:
                error_msg = f"未能到達社團頁面，當前URL: {current_url}"
                logger.error(error_msg)
                self.update_progress(0, error_msg)
                return False

        except Exception as e:
            error_msg = f"導航到社團頁面失敗: {e}"
            logger.error(error_msg)
            self.update_progress(0, error_msg)
            return False
    
    def scroll_and_load_all_groups(self) -> bool:
        """滾動載入所有社團 - 優化版本"""
        try:
            self.update_progress(35, "開始滾動載入所有社團...")

            scroll_attempts = 0
            max_scroll_attempts = 300  # 增加最大滾動次數以確保完整載入
            no_new_content_count = 0
            last_valid_group_count = 0
            consecutive_same_count = 0  # 連續相同數量的計數

            while scroll_attempts < max_scroll_attempts:
                scroll_attempts += 1

                # 執行滾動
                scroll_info = self.page.run_js("""
                var before = window.pageYOffset;
                var pageHeight = document.body.scrollHeight;
                var viewportHeight = window.innerHeight;

                window.scrollBy(0, 1000);  // 調整滾動距離

                var after = window.pageYOffset;
                var atBottom = (after + viewportHeight) >= (pageHeight - 100);  // 增加容差
                var canScroll = after > before;

                return {
                    before: before,
                    after: after,
                    pageHeight: pageHeight,
                    atBottom: atBottom,
                    scrolled: canScroll,
                    progress: Math.min((after + viewportHeight) / pageHeight * 100, 100)
                };
                """)

                # 每10次滾動顯示一次進度
                if scroll_attempts % 10 == 0:
                    progress = min(35 + int(scroll_attempts / max_scroll_attempts * 30), 65)
                    self.update_progress(progress, f"第 {scroll_attempts} 次滾動: {scroll_info['before']:.0f} -> {scroll_info['after']:.0f} ({scroll_info['progress']:.1f}%)")

                # 等待內容載入
                time.sleep(2)  # 增加等待時間確保內容載入

                # 檢查社團連結數量變化
                current_valid_group_count = self.page.run_js("""
                var allGroupLinks = document.querySelectorAll('a[href*="/groups/"]');
                var validCount = 0;

                for (var i = 0; i < allGroupLinks.length; i++) {
                    var link = allGroupLinks[i];
                    var href = link.href;

                    // 基本過濾：排除無效連結
                    if (href.includes('/groups/') &&
                        !href.includes('/groups/create') &&
                        !href.includes('/groups/browse') &&
                        !href.includes('/groups/search') &&
                        !href.includes('/groups/feed') &&
                        !href.includes('/groups/joins') &&
                        !href.includes('/groups/discover') &&
                        !href.includes('permalink') &&
                        !href.includes('notif_id') &&
                        !href.includes('notif_t') &&
                        !href.includes('multi_permalinks') &&
                        !href.includes('?category=') &&
                        href !== 'https://www.facebook.com/groups/' &&
                        !href.endsWith('/groups/')) {
                        validCount++;
                    }
                }

                return validCount;
                """)

                if current_valid_group_count > last_valid_group_count:
                    new_groups = current_valid_group_count - last_valid_group_count
                    logger.info(f"📈 載入了 {new_groups} 個新社團: {last_valid_group_count} -> {current_valid_group_count}")
                    last_valid_group_count = current_valid_group_count
                    no_new_content_count = 0
                    consecutive_same_count = 0
                elif current_valid_group_count == last_valid_group_count:
                    consecutive_same_count += 1
                    no_new_content_count += 1

                # 每5次滾動顯示當前狀態
                if scroll_attempts % 5 == 0:
                    logger.info(f"📊 當前狀態: 社團數={current_valid_group_count}, 無新內容次數={consecutive_same_count}")

                # 檢查停止條件
                if not scroll_info['scrolled']:
                    logger.info("📄 無法繼續滾動")
                    if consecutive_same_count >= 5:
                        logger.info("📄 確認到達底部且無新內容，停止滾動")
                        break
                    else:
                        logger.info("⏳ 等待更多內容載入...")
                        time.sleep(3)
                        continue

                # 如果連續很多次沒有新內容，停止滾動
                if consecutive_same_count >= 20:
                    logger.info(f"📄 連續{consecutive_same_count}次沒有新內容，停止滾動")
                    break

            self.update_progress(70, f"滾動完成，共滾動 {scroll_attempts} 次，發現 {last_valid_group_count} 個有效社團")
            return True

        except Exception as e:
            error_msg = f"滾動載入社團失敗: {e}"
            logger.error(error_msg)
            self.update_progress(0, error_msg)
            return False
    
    def extract_group_links(self) -> List[Dict]:
        """提取所有有效社團連結 - 改進版本"""
        try:
            self.update_progress(75, "提取有效社團連結...")

            # 使用簡化的JavaScript邏輯提取社團連結（先確保基本功能）
            group_info = self.page.run_js("""
            var validGroups = [];
            var processedIds = new Set();

            // 直接查找所有社團連結，不限制容器
            var allGroupLinks = document.querySelectorAll('a[href*="/groups/"]');

            console.log('找到', allGroupLinks.length, '個社團連結');

            for (var i = 0; i < allGroupLinks.length; i++) {
                var link = allGroupLinks[i];
                var href = link.href;

                // 改進社團名稱提取邏輯
                var groupName = '';

                // 方法1：直接從連結文字獲取
                if (link.textContent && link.textContent.trim()) {
                    groupName = link.textContent.trim();
                }

                // 方法2：從aria-label獲取
                if (!groupName && link.getAttribute('aria-label')) {
                    groupName = link.getAttribute('aria-label').trim();
                }

                // 方法3：從title屬性獲取
                if (!groupName && link.getAttribute('title')) {
                    groupName = link.getAttribute('title').trim();
                }

                // 方法4：從父容器的文字中提取
                if (!groupName) {
                    var parentDiv = link.closest('div[role="listitem"], div[data-pagelet], div[data-testid]');
                    if (parentDiv) {
                        var parentText = parentDiv.textContent.trim();
                        // 排除時間信息和其他無關文字
                        var cleanText = parentText.replace(/(上次造訪|分鐘前|小時前|天前|last visited|minutes? ago|hours? ago|days? ago).*$/i, '').trim();
                        if (cleanText && cleanText.length > 0 && cleanText.length < 100) {
                            groupName = cleanText;
                        }
                    }
                }

                // 清理社團名稱
                if (groupName) {
                    // 移除時間相關的文字 - 更全面的清理
                    groupName = groupName.replace(/上次發文時間：[^\\n]*/gi, '').trim();
                    groupName = groupName.replace(/上次造訪[^\\n]*/gi, '').trim();
                    groupName = groupName.replace(/\\d+\\s*分鐘前[^\\n]*/gi, '').trim();
                    groupName = groupName.replace(/\\d+\\s*小時前[^\\n]*/gi, '').trim();
                    groupName = groupName.replace(/\\d+\\s*天前[^\\n]*/gi, '').trim();
                    groupName = groupName.replace(/\\d+\\s*週前[^\\n]*/gi, '').trim();
                    groupName = groupName.replace(/\\d+\\s*月前[^\\n]*/gi, '').trim();
                    groupName = groupName.replace(/last\\s+(visited|posted)[^\\n]*/gi, '').trim();
                    groupName = groupName.replace(/\\d+\\s*(minutes?|hours?|days?|weeks?|months?)\\s+ago[^\\n]*/gi, '').trim();

                    // 移除Facebook UI相關的文字
                    groupName = groupName.replace(/你加入了/gi, '').trim();
                    groupName = groupName.replace(/你上次/gi, '').trim();
                    groupName = groupName.replace(/你的/gi, '').trim();

                    // 移除末尾的"你"字（Facebook UI元素）
                    groupName = groupName.replace(/你$/g, '').trim();

                    // 移除多餘的空白和特殊字符
                    groupName = groupName.replace(/\\s+/g, ' ').trim();

                    // 如果名稱太長，截取前40個字符
                    if (groupName.length > 40) {
                        groupName = groupName.substring(0, 40) + '...';
                    }

                    // 如果清理後名稱為空，設為空字符串
                    if (groupName.length === 0) {
                        groupName = '';
                    }
                }

                // 排除無效連結
                if (href.includes('/groups/create') ||
                    href.includes('/groups/browse') ||
                    href.includes('/groups/search') ||
                    href.includes('/groups/feed') ||
                    href.includes('/groups/joins') ||
                    href.includes('/groups/discover') ||
                    href.includes('permalink') ||
                    href.includes('notif_id') ||
                    href.includes('notif_t') ||
                    href.includes('multi_permalinks') ||
                    href.includes('?category=') ||
                    href === 'https://www.facebook.com/groups/' ||
                    href.endsWith('/groups/')) {
                    continue;
                }

                // 提取純淨的社團ID（支持數字ID和英文ID）
                // 只匹配 /groups/ID/ 或 /groups/ID 格式，排除帶參數的連結
                var cleanMatch = href.match(/\\/groups\\/([a-zA-Z0-9._-]+)(?:\\/|$)/);

                var groupId = null;
                var groupUrl = null;

                if (cleanMatch && cleanMatch[1]) {
                    groupId = cleanMatch[1];

                    // 檢查是否為有效的社團ID
                    var isValidId = false;

                    if (/^[0-9]+$/.test(groupId)) {
                        // 數字ID：至少6位數字
                        if (groupId.length >= 6) {
                            isValidId = true;
                        }
                    } else {
                        // 英文ID：至少3個字符，不能是系統保留詞
                        var reservedWords = ['discover', 'create', 'browse', 'search', 'feed', 'joins'];
                        if (groupId.length >= 3 && !reservedWords.includes(groupId.toLowerCase())) {
                            isValidId = true;
                        }
                    }

                    if (isValidId) {
                        groupUrl = 'https://www.facebook.com/groups/' + groupId;
                    }
                }

                // 檢查是否有效且未重複
                if (groupUrl && groupId && !processedIds.has(groupId)) {
                    processedIds.add(groupId);

                    // 嘗試從父容器獲取更多信息
                    var parentContainer = link.closest('div');
                    var containerText = parentContainer ? parentContainer.textContent : '';

                    // 檢查是否有造訪時間信息
                    var hasVisitInfo = containerText.includes('上次造訪') ||
                                     containerText.includes('分鐘前') ||
                                     containerText.includes('小時前') ||
                                     containerText.includes('天前') ||
                                     containerText.includes('last visited') ||
                                     containerText.includes('minutes ago') ||
                                     containerText.includes('hours ago') ||
                                     containerText.includes('days ago');

                    var visitTimeMatch = containerText.match(/(\\d+)\\s*(分鐘|小時|天|minutes?|hours?|days?)前/);
                    var visitTime = visitTimeMatch ? visitTimeMatch[0] : (hasVisitInfo ? '有造訪記錄' : '未知');

                    // 正確的數字ID檢測邏輯
                    var isNumericId = /^[0-9]+$/.test(groupId);

                    // 如果還是沒有社團名稱，使用社團ID作為名稱
                    var finalGroupName = groupName;
                    if (!finalGroupName || finalGroupName.trim() === '') {
                        finalGroupName = '社團 ' + groupId;
                    }

                    validGroups.push({
                        id: groupId,
                        url: groupUrl,
                        name: finalGroupName,
                        visitTime: visitTime,
                        isNumeric: isNumericId,
                        hasVisitInfo: hasVisitInfo
                    });
                }
            }

            console.log('提取到', validGroups.length, '個有效社團');

            // 優先顯示有造訪信息的社團
            validGroups.sort(function(a, b) {
                if (a.hasVisitInfo && !b.hasVisitInfo) return -1;
                if (!a.hasVisitInfo && b.hasVisitInfo) return 1;
                return 0;
            });

            return validGroups;
            """)

            if group_info:
                # 返回完整的社團信息，而不只是URL列表
                numeric_count = sum(1 for group in group_info if group['isNumeric'])
                alpha_count = len(group_info) - numeric_count

                self.update_progress(80, f"成功提取 {len(group_info)} 個有效社團連結 (數字ID: {numeric_count}, 英文ID: {alpha_count})")

                # 顯示前5個社團的詳細信息
                logger.info(f"前5個有效社團:")
                for i, group in enumerate(group_info[:5], 1):
                    logger.info(f"   {i}. {group['name']} ({group['visitTime']})")
                    logger.info(f"      ID: {group['id']} | URL: {group['url']}")

                return group_info  # 返回完整的社團信息
            else:
                error_msg = "未提取到任何有效社團連結"
                logger.warning(error_msg)
                self.update_progress(0, error_msg)
                return []

        except Exception as e:
            error_msg = f"提取社團連結失敗: {e}"
            logger.error(error_msg)
            self.update_progress(0, error_msg)
            return []
    
    def generate_user_profile_urls(self, group_info: List[Dict]) -> List[str]:
        """生成社團個人動態頁面URL"""
        if not self.user_id:
            error_msg = "用戶ID未設定，無法生成個人動態URL"
            logger.error(error_msg)
            self.update_progress(0, error_msg)
            return []

        try:
            self.update_progress(85, f"生成社團個人動態頁面URL (用戶ID: {self.user_id})...")

            user_profile_urls = []

            # 處理新格式的group_info（包含完整信息的字典列表）
            if group_info and isinstance(group_info[0], dict):
                for group in group_info:
                    group_id = group['id']
                    user_profile_url = f"https://www.facebook.com/groups/{group_id}/user/{self.user_id}"
                    user_profile_urls.append(user_profile_url)

                    # 調試輸出
                    if not group_id.isdigit():
                        logger.info(f"英文ID社團: {group_id} -> {user_profile_url}")
            else:
                # 向後兼容：處理舊格式的URL列表
                for group_url in group_info:
                    match = re.search(r'/groups/([a-zA-Z0-9._-]+)', group_url)
                    if match:
                        group_id = match.group(1)
                        user_profile_url = f"https://www.facebook.com/groups/{group_id}/user/{self.user_id}"
                        user_profile_urls.append(user_profile_url)

            self.update_progress(90, f"成功生成 {len(user_profile_urls)} 個個人動態頁面URL")
            return user_profile_urls

        except Exception as e:
            error_msg = f"生成個人動態URL失敗: {e}"
            logger.error(error_msg)
            self.update_progress(0, error_msg)
            return []
    
    def save_urls_to_file(self, urls: List[str], filename: str = None) -> bool:
        """保存URL到文件"""
        try:
            # 如果沒有指定文件名，保存到當前工作目錄
            if filename is None:
                filename = "urls.txt"

            self.update_progress(95, f"保存URL到文件: {filename}")

            with open(filename, 'w', encoding='utf-8') as f:
                for url in urls:
                    f.write(url + '\n')

            self.update_progress(98, f"成功保存 {len(urls)} 個URL到 {filename}")
            return True

        except Exception as e:
            error_msg = f"保存URL到文件失敗: {e}"
            logger.error(error_msg)
            self.update_progress(0, error_msg)
            return False
    
    def clean_unicode_text(self, text: str) -> str:
        """清理Unicode文本，移除代理字符和其他問題字符"""
        if not text:
            return ""

        try:
            # 移除代理字符（surrogates）
            cleaned = text.encode('utf-8', errors='ignore').decode('utf-8')

            # 移除控制字符，但保留常見的空白字符
            import re
            cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)

            return cleaned.strip()
        except Exception:
            # 如果清理失敗，返回安全的替代文本
            return "無法顯示的文本"

    def clean_group_info(self, group_info: List[dict]) -> List[dict]:
        """清理社團信息中的問題字符"""
        cleaned_groups = []

        for group in group_info:
            cleaned_group = {}
            for key, value in group.items():
                if isinstance(value, str):
                    cleaned_group[key] = self.clean_unicode_text(value)
                else:
                    cleaned_group[key] = value
            cleaned_groups.append(cleaned_group)

        return cleaned_groups

    def save_crawl_info(self, group_info: List[dict], user_profile_urls: List[str], filename: str = None) -> bool:
        """保存抓取信息到JSON文件"""
        try:
            # 如果沒有指定文件名，保存到當前工作目錄
            if filename is None:
                filename = "crawl_info.json"

            # 清理社團信息中的問題字符
            cleaned_group_info = self.clean_group_info(group_info)

            # 為了向後兼容，同時保存完整信息和URL列表
            group_links = [group['url'] for group in cleaned_group_info] if isinstance(cleaned_group_info[0], dict) else cleaned_group_info

            crawl_info = {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "user_id": self.user_id,
                "total_groups": len(cleaned_group_info),
                "group_links": group_links,  # 保持向後兼容
                "groups": cleaned_group_info,  # 新增：完整的社團信息
                "user_profile_urls": user_profile_urls
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(crawl_info, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ 抓取信息已保存到 {filename}")
            return True

        except Exception as e:
            logger.error(f"保存抓取信息失敗: {e}")
            # 嘗試使用ASCII模式保存
            try:
                logger.info("嘗試使用ASCII模式保存...")
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(crawl_info, f, ensure_ascii=True, indent=2)
                logger.info(f"✅ 使用ASCII模式保存成功: {filename}")
                return True
            except Exception as e2:
                logger.error(f"ASCII模式保存也失敗: {e2}")
                return False
    
    def crawl_all_groups(self, use_existing_browser: bool = True, save_to_file: bool = True) -> Dict:
        """完整的社團抓取流程 - 後端API版本"""
        try:
            self.update_progress(0, "開始完整的社團抓取流程...")

            # 1. 設定瀏覽器
            if not self.setup_browser(use_existing_browser):
                return {"success": False, "message": self.current_message}

            # 2. 提取用戶ID
            if not self.extract_user_id_from_cookies():
                return {"success": False, "message": self.current_message}

            # 3. 導航到社團頁面
            if not self.navigate_to_groups_page():
                return {"success": False, "message": self.current_message}

            # 4. 滾動載入所有社團
            if not self.scroll_and_load_all_groups():
                return {"success": False, "message": self.current_message}

            # 5. 提取社團連結
            group_info = self.extract_group_links()
            if not group_info:
                return {"success": False, "message": self.current_message}

            # 6. 生成個人動態頁面URL
            user_profile_urls = self.generate_user_profile_urls(group_info)
            if not user_profile_urls:
                return {"success": False, "message": self.current_message}

            # 7. 保存URL到文件（可選）
            if save_to_file:
                if not self.save_urls_to_file(user_profile_urls):
                    return {"success": False, "message": self.current_message}

                # 8. 保存抓取信息
                self.save_crawl_info(group_info, user_profile_urls)

            self.update_progress(100, "社團抓取流程完成!")

            # 提取URL列表用於向後兼容
            group_urls = [group['url'] for group in group_info] if isinstance(group_info[0], dict) else group_info

            return {
                "success": True,
                "user_id": self.user_id,
                "total_groups": len(group_info),
                "groups": group_info,  # 完整的社團信息
                "group_urls": group_urls,  # 向後兼容
                "user_profile_urls": user_profile_urls,
                "progress": self.current_progress,
                "message": self.current_message
            }

        except Exception as e:
            error_msg = f"社團抓取流程失敗: {str(e)}"
            logger.error(error_msg)
            self.update_progress(0, error_msg)
            return {"success": False, "message": error_msg}

def main():
    """主函數 - 測試社團抓取功能"""
    print("=" * 60)
    print("Facebook 社團抓取器")
    print("=" * 60)
    
    crawler = FacebookGroupsCrawler()
    result = crawler.crawl_all_groups()
    
    if result["success"]:
        print(f"\n✅ 抓取成功!")
        print(f"用戶ID: {result['user_id']}")
        print(f"社團數量: {result['total_groups']}")
        print(f"已保存到 urls.txt")
    else:
        print(f"\n❌ 抓取失敗: {result['message']}")
    
    print("\n完成。")

if __name__ == "__main__":
    main()
