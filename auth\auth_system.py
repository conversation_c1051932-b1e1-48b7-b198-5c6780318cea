import os
import uuid
import hashlib
import platform
from datetime import datetime, timedelta
from supabase import create_client, Client
from dotenv import load_dotenv

# 嘗試載入.env文件（開發環境）
try:
    load_dotenv()
except:
    pass  # 打包後可能沒有.env文件

# 嘗試導入配置，支持不同的導入方式
try:
    from .config import Config
except ImportError:
    try:
        from config import Config
    except ImportError:
        # 如果都失敗，使用內置配置
        class Config:
            SUPABASE_URL = "https://clyhepqziqkzufzeqrsc.supabase.co"
            SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNseWhlcHF6aXFrenVmemVxcnNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwMzk2NTIsImV4cCI6MjA2OTYxNTY1Mn0.10wSOqHOOS8tyYEwF3oUzVZRc3voA9f-KPNcDvyQZLo"
            AUTO_BIND_ENABLED = False

            @classmethod
            def get_supabase_url(cls):
                return os.environ.get("SUPABASE_URL", cls.SUPABASE_URL)

            @classmethod
            def get_supabase_key(cls):
                return os.environ.get("SUPABASE_KEY", cls.SUPABASE_KEY)

            @classmethod
            def is_auto_bind_enabled(cls):
                env_value = os.environ.get("AUTO_BIND_ENABLED", str(cls.AUTO_BIND_ENABLED))
                return env_value.lower() in ('true', '1', 'yes', 'on')

class DeviceAuthSystem:
    def __init__(self):
        # 使用配置管理類獲取配置
        url = Config.get_supabase_url()
        key = Config.get_supabase_key()

        if not url or not key:
            raise Exception("Supabase configuration not found")

        self.supabase: Client = create_client(url, key)
    
    def get_device_id(self):
        """獲取設備唯一標識"""
        try:
            # 組合硬件信息
            machine_id = platform.machine()
            processor = platform.processor()
            node = platform.node()
            
            # MAC 地址
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> i) & 0xff) 
                           for i in range(0,8*6,8)][::-1])
            
            # 生成設備ID
            device_info = f"{machine_id}-{processor}-{node}-{mac}"
            device_id = hashlib.sha256(device_info.encode()).hexdigest()[:16]
            return device_id
        except Exception as e:
            # 備用方案
            return hashlib.sha256(str(uuid.getnode()).encode()).hexdigest()[:16]
    
    def verify_authorization(self, user_id: str, device_id: str = None):
        """驗證用戶和設備授權 - 強制Facebook ID綁定"""
        if device_id is None:
            device_id = self.get_device_id()

        try:
            # 首先檢查這個設備是否已經綁定了其他Facebook ID
            device_bindings = self.supabase.table('device_authorizations').select('*').eq(
                'device_id', device_id
            ).eq('is_active', True).execute()

            if device_bindings.data:
                # 設備已有綁定，檢查是否是同一個用戶
                bound_user_id = device_bindings.data[0]['user_id']

                if bound_user_id != user_id:
                    # 不同的Facebook ID嘗試使用已綁定的設備 - 記錄為可疑活動
                    self.log_usage(user_id, device_id, "unauthorized_attempt")

                    return {
                        "authorized": False,
                        "reason": "device_bound_to_different_user",
                        "message": f"此設備已綁定到Facebook ID: {bound_user_id}，當前登入ID: {user_id}",
                        "bound_user_id": bound_user_id,
                        "current_user_id": user_id
                    }

                # 是同一個用戶，檢查授權狀態
                auth_record = device_bindings.data[0]

                # 檢查過期時間
                if auth_record.get('expires_at'):
                    expires_at = datetime.fromisoformat(auth_record['expires_at'].replace('Z', '+00:00'))
                    if expires_at < datetime.now(expires_at.tzinfo):
                        return {
                            "authorized": False,
                            "reason": "authorization_expired",
                            "message": "授權已過期"
                        }

                # 記錄使用日誌
                self.log_usage(user_id, device_id, "verify_success")

                return {
                    "authorized": True,
                    "permissions": auth_record.get('permissions', []),
                    "expires_at": auth_record.get('expires_at'),
                    "device_name": auth_record.get('device_name'),
                    "bound_user_id": bound_user_id
                }

            else:
                # 設備未綁定任何用戶
                return {
                    "authorized": False,
                    "reason": "device_not_bound",
                    "message": "此設備尚未綁定任何Facebook帳號"
                }

        except Exception as e:
            return {
                "authorized": False,
                "reason": "verification_error",
                "message": f"驗證失敗: {str(e)}"
            }
    
    def register_device_with_code(self, auth_code: str, facebook_user_id: str, device_name: str = None):
        """使用授權碼註冊設備並綁定Facebook ID"""
        device_id = self.get_device_id()

        try:
            # 驗證授權碼
            code_result = self.supabase.table('auth_codes').select('*').eq(
                'code', auth_code
            ).eq('is_active', True).execute()

            if not code_result.data:
                return {
                    "success": False,
                    "message": "無效的授權碼"
                }

            code_record = code_result.data[0]

            # 檢查授權碼是否過期
            if code_record.get('expires_at'):
                expires_at = datetime.fromisoformat(code_record['expires_at'].replace('Z', '+00:00'))
                if expires_at < datetime.now(expires_at.tzinfo):
                    return {
                        "success": False,
                        "message": "授權碼已過期"
                    }

            # 檢查設備槽位
            if code_record['used_slots'] >= code_record['device_slots']:
                return {
                    "success": False,
                    "message": "授權碼設備槽位已滿"
                }

            # 檢查設備是否已經綁定了其他Facebook ID
            existing_binding = self.supabase.table('device_authorizations').select('*').eq(
                'device_id', device_id
            ).eq('is_active', True).execute()

            if existing_binding.data:
                bound_user = existing_binding.data[0]['user_id']
                if bound_user != facebook_user_id:
                    return {
                        "success": False,
                        "message": f"此設備已綁定到Facebook ID: {bound_user}"
                    }

            # 計算授權有效期（從授權碼的有效期或默認30天）
            if code_record.get('expires_at'):
                auth_expires_at = code_record['expires_at']
            else:
                # 默認30天有效期
                auth_expires_at = (datetime.now() + timedelta(days=30)).isoformat()

            # 註冊設備並綁定Facebook ID
            device_name = device_name or f"Device-{device_id[:8]}"

            auth_data = {
                'user_id': facebook_user_id,  # 綁定Facebook ID
                'device_id': device_id,
                'device_name': device_name,
                'expires_at': auth_expires_at,
                'is_active': True,
                'permissions': ["crawl", "delete"]
            }

            self.supabase.table('device_authorizations').upsert(auth_data).execute()

            # 更新授權碼使用次數
            self.supabase.table('auth_codes').update({
                'used_slots': code_record['used_slots'] + 1
            }).eq('id', code_record['id']).execute()

            # 記錄日誌
            self.log_usage(facebook_user_id, device_id, "device_bound_with_code")

            return {
                "success": True,
                "message": "設備註冊並綁定Facebook ID成功",
                "device_id": device_id,
                "device_name": device_name,
                "facebook_user_id": facebook_user_id,
                "expires_at": auth_expires_at
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"註冊失敗: {str(e)}"
            }
    
    def auto_bind_device(self, user_id: str, device_id: str = None, device_name: str = None):
        """自動綁定設備到用戶"""
        if device_id is None:
            device_id = self.get_device_id()

        try:
            # 檢查是否已經綁定
            existing = self.supabase.table('device_authorizations').select('*').eq(
                'user_id', user_id
            ).eq('device_id', device_id).execute()

            if existing.data:
                # 已經綁定，更新最後使用時間
                self.supabase.table('device_authorizations').update({
                    'authorized_at': datetime.now().isoformat()
                }).eq('user_id', user_id).eq('device_id', device_id).execute()

                return {
                    "success": True,
                    "message": "設備已綁定",
                    "device_id": device_id,
                    "existing": True
                }

            # 新綁定
            device_name = device_name or f"Auto-{device_id[:8]}"

            auth_data = {
                'user_id': user_id,
                'device_id': device_id,
                'device_name': device_name,
                'expires_at': None,  # 永久授權
                'is_active': True,
                'permissions': ["crawl", "delete"]  # 默認全權限
            }

            self.supabase.table('device_authorizations').insert(auth_data).execute()

            # 記錄日誌
            self.log_usage(user_id, device_id, "auto_bind_success")

            return {
                "success": True,
                "message": "設備自動綁定成功",
                "device_id": device_id,
                "device_name": device_name,
                "existing": False
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"自動綁定失敗: {str(e)}"
            }

    def is_auto_bind_enabled(self):
        """檢查是否啟用自動綁定"""
        return Config.is_auto_bind_enabled()

    def log_usage(self, user_id: str, device_id: str, action: str):
        """記錄使用日誌"""
        try:
            self.supabase.table('usage_logs').insert({
                'user_id': user_id,
                'device_id': device_id,
                'action': action
            }).execute()
        except:
            pass  # 日誌失敗不影響主要功能

# 全局實例
auth_system = DeviceAuthSystem()
