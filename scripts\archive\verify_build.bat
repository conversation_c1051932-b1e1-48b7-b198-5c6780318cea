@echo off
echo Verifying build for user data...
echo.

set "FOUND_ISSUES=0"

REM Check dist directory
if exist "dist\FacebookTool\_internal\urls.txt" (
    echo ISSUE: urls.txt found in build!
    set "FOUND_ISSUES=1"
)

if exist "dist\FacebookTool\_internal\crawl_info.json" (
    echo ISSUE: crawl_info.json found in build!
    set "FOUND_ISSUES=1"
)

if exist "dist\FacebookTool\_internal\facebook_deleter.log" (
    echo ISSUE: facebook_deleter.log found in build!
    set "FOUND_ISSUES=1"
)

REM Search for user ID patterns in text files
echo Searching for potential user IDs...
for /r "dist" %%f in (*.txt *.json *.log) do (
    if exist "%%f" (
        findstr /c:"100000" "%%f" >nul 2>&1
        if not errorlevel 1 (
            echo POTENTIAL ISSUE: User ID pattern found in %%f
            set "FOUND_ISSUES=1"
        )
    )
)

if "%FOUND_ISSUES%"=="0" (
    echo.
    echo ✅ Build verification PASSED!
    echo No user data found in build.
    echo Safe for distribution.
) else (
    echo.
    echo ❌ Build verification FAILED!
    echo User data found in build.
    echo DO NOT distribute this build!
)

echo.
pause
