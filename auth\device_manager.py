try:
    from .auth_system import auth_system
except ImportError:
    from auth_system import auth_system
from datetime import datetime

def list_device_bindings():
    """列出所有設備綁定"""
    try:
        result = auth_system.supabase.table('device_authorizations').select('*').order('created_at', desc=True).execute()
        
        print("\n設備綁定列表:")
        print("=" * 100)
        print(f"{'設備ID':<20} {'Facebook ID':<20} {'設備名稱':<20} {'狀態':<10} {'綁定時間':<20}")
        print("-" * 100)
        
        for device in result.data:
            status = "有效" if device['is_active'] else "無效"
            bind_time = datetime.fromisoformat(device['authorized_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
            
            print(f"{device['device_id']:<20} {device['user_id']:<20} {device['device_name']:<20} {status:<10} {bind_time:<20}")
        
        print("-" * 100)
        print(f"總計: {len(result.data)} 個設備綁定")
            
    except Exception as e:
        print(f"查詢失敗: {e}")

def check_device_binding(device_id):
    """檢查特定設備的綁定狀態"""
    try:
        result = auth_system.supabase.table('device_authorizations').select('*').eq(
            'device_id', device_id
        ).execute()
        
        if not result.data:
            print(f"設備 {device_id} 未綁定任何Facebook帳號")
            return
        
        device = result.data[0]
        print(f"\n設備綁定信息:")
        print(f"設備ID: {device['device_id']}")
        print(f"綁定Facebook ID: {device['user_id']}")
        print(f"設備名稱: {device['device_name']}")
        print(f"狀態: {'有效' if device['is_active'] else '無效'}")
        print(f"權限: {device.get('permissions', [])}")
        print(f"綁定時間: {datetime.fromisoformat(device['authorized_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')}")
        
        if device.get('expires_at'):
            expires = datetime.fromisoformat(device['expires_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            print(f"過期時間: {expires}")
        else:
            print(f"過期時間: 永久")
            
    except Exception as e:
        print(f"查詢失敗: {e}")

def unbind_device(device_id):
    """解除設備綁定"""
    try:
        # 先檢查設備是否存在
        result = auth_system.supabase.table('device_authorizations').select('*').eq(
            'device_id', device_id
        ).execute()
        
        if not result.data:
            print(f"設備 {device_id} 未找到")
            return False
        
        device = result.data[0]
        print(f"即將解除綁定:")
        print(f"設備ID: {device['device_id']}")
        print(f"Facebook ID: {device['user_id']}")
        print(f"設備名稱: {device['device_name']}")
        
        confirm = input("\n確定要解除此設備綁定嗎？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return False
        
        # 刪除綁定記錄
        auth_system.supabase.table('device_authorizations').delete().eq(
            'device_id', device_id
        ).execute()
        
        print(f"✅ 設備 {device_id} 綁定已解除")
        return True
        
    except Exception as e:
        print(f"解除綁定失敗: {e}")
        return False

def rebind_device(device_id, new_user_id):
    """重新綁定設備到新的Facebook ID"""
    try:
        # 先解除舊綁定
        auth_system.supabase.table('device_authorizations').delete().eq(
            'device_id', device_id
        ).execute()
        
        # 創建新綁定
        auth_data = {
            'user_id': new_user_id,
            'device_id': device_id,
            'device_name': f"Rebound-{device_id[:8]}",
            'expires_at': None,
            'is_active': True,
            'permissions': ["crawl", "delete"]
        }
        
        auth_system.supabase.table('device_authorizations').insert(auth_data).execute()
        
        print(f"✅ 設備 {device_id} 已重新綁定到 Facebook ID: {new_user_id}")
        return True
        
    except Exception as e:
        print(f"重新綁定失敗: {e}")
        return False

def get_current_device_id():
    """獲取當前設備ID"""
    device_id = auth_system.get_device_id()
    print(f"當前設備ID: {device_id}")
    return device_id

def main():
    print("設備綁定管理工具")
    print("=" * 50)
    print("防止工具濫用 - Facebook ID 與設備綁定管理")
    
    while True:
        print(f"\n選擇操作:")
        print("1. 查看當前設備ID")
        print("2. 列出所有設備綁定")
        print("3. 檢查特定設備綁定")
        print("4. 解除設備綁定")
        print("5. 重新綁定設備")
        print("6. 退出")
        
        choice = input("\n請選擇 (1-6): ").strip()
        
        if choice == "1":
            get_current_device_id()
            
        elif choice == "2":
            list_device_bindings()
            
        elif choice == "3":
            device_id = input("請輸入設備ID: ").strip()
            if device_id:
                check_device_binding(device_id)
            
        elif choice == "4":
            device_id = input("請輸入要解除綁定的設備ID: ").strip()
            if device_id:
                unbind_device(device_id)
            
        elif choice == "5":
            device_id = input("請輸入設備ID: ").strip()
            new_user_id = input("請輸入新的Facebook ID: ").strip()
            if device_id and new_user_id:
                rebind_device(device_id, new_user_id)
            
        elif choice == "6":
            break
            
        else:
            print("無效選擇，請重試")

if __name__ == "__main__":
    main()
