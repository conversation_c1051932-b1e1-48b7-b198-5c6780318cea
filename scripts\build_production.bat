@echo off
chcp 65001 >nul
echo Facebook Auto - Production Build
echo ===================================

echo Step 1: Development cleanup
cd ..
call scripts\clean_dev.bat

echo Step 2: Create isolated build environment
if exist "scripts\production_build" rmdir /s /q "scripts\production_build"
mkdir scripts\production_build
cd scripts\production_build

echo Step 3: Copy only source code - NO DATA FILES
copy ..\..\*.py .
copy ..\..\requirements.txt .
xcopy ..\..\templates templates\ /e /i
if exist ..\..\static xcopy ..\..\static static\ /e /i
xcopy ..\..\auth auth\ /e /i

echo Step 4: Checking for user data files
set FOUND_DATA=0
if exist "urls.txt" set FOUND_DATA=1
if exist "crawl_info.json" set FOUND_DATA=1
if exist "facebook_deleter.log" set FOUND_DATA=1

if "%FOUND_DATA%"=="1" (
    echo WARNING: Found user data files in build directory!
    echo This could leak personal information!
    echo Please run clean_dev.bat first
    pause
    exit /b 1
)

echo Step 5: Install dependencies
pip install pyinstaller

echo Step 6: Build production executable
pyinstaller --onefile --console --exclude-module tkinter --exclude-module matplotlib --exclude-module numpy --exclude-module pandas --add-data "templates;templates" --add-data "auth;auth" --name FacebookTool_Production app.py

if exist "dist\FacebookTool_Production.exe" (
    echo Step 7: Build successful!
    
    echo Step 8: Final security check
    cd ..\..
    python scripts\check_exe_content.py "scripts\production_build\dist\FacebookTool_Production.exe"

    echo Step 9: Move to distribution
    if not exist "release" mkdir "release"
    copy "scripts\production_build\dist\FacebookTool_Production.exe" "release\"

    echo Step 10: Create distribution package
    cd release
    copy "..\scripts\start_template.bat" "start_facebook_tool.bat"
    copy "..\scripts\README_RELEASE.txt" "README.txt"
    cd ..

    echo Step 11: Cleanup build directory
    rmdir /s /q "scripts\production_build"

    echo.
    echo ===================================
    echo Production build completed!
    echo Distribution files in: release\
    echo Executable: FacebookTool_Production.exe
    echo Launcher: start_facebook_tool.bat
    echo.
    echo IMPORTANT: Test the executable before distribution!
    echo ===================================

) else (
    echo Build failed!
)

pause
