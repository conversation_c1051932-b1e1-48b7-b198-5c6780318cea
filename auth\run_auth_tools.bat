@echo off
echo Facebook Tool - Auth Management
echo ==================================

:menu
echo.
echo Select operation:
echo 1. Web Admin Panel (Recommended)
echo 2. Create auth code (CLI)
echo 3. Manage devices (CLI)
echo 4. Monitor usage (CLI)
echo 5. Clean database (Remove test data)
echo 6. Test anti-abuse
echo 7. Test Supabase connection
echo 8. Exit

set /p choice="Please choose (1-8): "

if "%choice%"=="1" (
    echo Starting Web Admin Panel...
    call start_admin.bat
    goto menu
)

if "%choice%"=="2" (
    echo Starting auth code creator...
    python create_auth_code.py
    goto menu
)

if "%choice%"=="3" (
    echo Starting device manager...
    python device_manager.py
    goto menu
)

if "%choice%"=="4" (
    echo Starting usage monitor...
    python usage_monitor.py
    goto menu
)

if "%choice%"=="5" (
    echo Starting database cleaner...
    python clean_database.py
    goto menu
)

if "%choice%"=="6" (
    echo Testing anti-abuse system...
    python test_anti_abuse.py
    pause
    goto menu
)

if "%choice%"=="7" (
    echo Testing Supabase connection...
    python test_supabase.py
    pause
    goto menu
)

if "%choice%"=="8" (
    echo Goodbye!
    exit /b 0
)

echo Invalid choice, please try again.
goto menu
