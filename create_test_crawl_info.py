#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
創建測試用的 crawl_info.json 文件
"""

import json

def create_test_crawl_info():
    """創建測試用的社團數據"""
    test_data = {
        "user_id": "100000389581396",
        "groups": [
            {
                "id": "test_group_1",
                "name": "測試社團1",
                "url": "https://www.facebook.com/groups/test_group_1",
                "member_count": "1.2K 位成員"
            },
            {
                "id": "test_group_2", 
                "name": "測試社團2",
                "url": "https://www.facebook.com/groups/test_group_2",
                "member_count": "5.6K 位成員"
            },
            {
                "id": "test_group_3",
                "name": "測試社團3", 
                "url": "https://www.facebook.com/groups/test_group_3",
                "member_count": "890 位成員"
            }
        ],
        "user_profile_urls": [
            "https://www.facebook.com/groups/test_group_1/user/100000389581396",
            "https://www.facebook.com/groups/test_group_2/user/100000389581396", 
            "https://www.facebook.com/groups/test_group_3/user/100000389581396"
        ],
        "total_groups": 3,
        "crawl_time": "2025-08-01 21:00:00"
    }
    
    try:
        with open('crawl_info.json', 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print("✅ 已創建測試用的 crawl_info.json 文件")
        print(f"   - 用戶ID: {test_data['user_id']}")
        print(f"   - 社團數量: {test_data['total_groups']}")
        print("現在可以測試社團列表的自動載入功能了！")
        
        return True
        
    except Exception as e:
        print(f"❌ 創建測試文件失敗: {e}")
        return False

if __name__ == "__main__":
    create_test_crawl_info()
