@echo off
echo Thorough cleaning for safe packaging...

REM Clean current directory
echo Cleaning current directory...
if exist "urls.txt" del "urls.txt"
if exist "crawl_info.json" del "crawl_info.json"
if exist "facebook_deleter.log" del "facebook_deleter.log"

REM Clean temp directory
echo Cleaning temp directory...
if exist "%TEMP%\urls.txt" del "%TEMP%\urls.txt"
if exist "%TEMP%\crawl_info.json" del "%TEMP%\crawl_info.json"
if exist "%TEMP%\facebook_deleter.log" del "%TEMP%\facebook_deleter.log"

REM Clean user temp directory
echo Cleaning user temp directory...
if exist "%USERPROFILE%\AppData\Local\Temp\urls.txt" del "%USERPROFILE%\AppData\Local\Temp\urls.txt"
if exist "%USERPROFILE%\AppData\Local\Temp\crawl_info.json" del "%USERPROFILE%\AppData\Local\Temp\crawl_info.json"
if exist "%USERPROFILE%\AppData\Local\Temp\facebook_deleter.log" del "%USERPROFILE%\AppData\Local\Temp\facebook_deleter.log"

REM Clean Python cache
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

REM Clean any .pyc files
for /r %%i in (*.pyc) do (
    if exist "%%i" del "%%i"
)

echo Thorough cleaning completed!
pause
