#!/usr/bin/env python3
"""
測試運行時文件創建
"""

import os
import tempfile
import time

def test_file_locations():
    """測試文件會在哪裡創建"""
    
    print("測試文件創建位置...")
    print(f"當前工作目錄: {os.getcwd()}")
    print(f"臨時目錄: {tempfile.gettempdir()}")
    print(f"用戶臨時目錄: {os.path.expanduser('~')}/AppData/Local/Temp")
    
    # 測試創建文件
    test_files = ['urls.txt', 'crawl_info.json', 'facebook_deleter.log']
    
    for filename in test_files:
        # 當前目錄
        current_path = filename
        temp_path = os.path.join(tempfile.gettempdir(), filename)
        
        print(f"\n測試文件: {filename}")
        print(f"  當前目錄路徑: {current_path}")
        print(f"  臨時目錄路徑: {temp_path}")
        
        # 檢查是否存在
        if os.path.exists(current_path):
            print(f"  ❌ 當前目錄中存在: {current_path}")
            with open(current_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()[:100]  # 只讀前100字符
                print(f"  內容預覽: {content}")
        
        if os.path.exists(temp_path):
            print(f"  ❌ 臨時目錄中存在: {temp_path}")
            with open(temp_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()[:100]
                print(f"  內容預覽: {content}")
        
        if not os.path.exists(current_path) and not os.path.exists(temp_path):
            print(f"  ✅ 未找到文件")

def check_all_possible_locations():
    """檢查所有可能的文件位置"""
    
    locations = [
        os.getcwd(),
        tempfile.gettempdir(),
        os.path.expanduser('~'),
        os.path.expanduser('~/AppData/Local/Temp'),
        os.path.expanduser('~/Documents'),
        os.path.expanduser('~/Desktop'),
    ]
    
    files_to_check = ['urls.txt', 'crawl_info.json', 'facebook_deleter.log']
    
    print("\n檢查所有可能的文件位置...")
    
    found_files = []
    
    for location in locations:
        if os.path.exists(location):
            for filename in files_to_check:
                filepath = os.path.join(location, filename)
                if os.path.exists(filepath):
                    found_files.append(filepath)
                    print(f"❌ 發現文件: {filepath}")
                    
                    # 檢查文件內容是否包含用戶ID
                    try:
                        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            if '100000' in content:
                                print(f"  ⚠️ 包含用戶ID!")
                    except:
                        pass
    
    if not found_files:
        print("✅ 未發現任何用戶數據文件")
    
    return found_files

if __name__ == "__main__":
    print("=" * 60)
    print("運行時文件檢查工具")
    print("=" * 60)
    
    test_file_locations()
    found = check_all_possible_locations()
    
    print("\n" + "=" * 60)
    if found:
        print(f"❌ 發現 {len(found)} 個用戶數據文件")
        print("建議運行清理腳本")
    else:
        print("✅ 環境乾淨，無用戶數據文件")
    print("=" * 60)
