# 📦 Facebook 工具打包指南

## ⚠️ 重要安全提醒
**在打包前必須清理用戶數據！** 詳見 [SAFE_BUILD_GUIDE.md](SAFE_BUILD_GUIDE.md)

## 🎯 目標
將 Facebook 工具打包成獨立的可執行文件，讓其他電腦無需安裝 Python 即可運行。

## 🛠️ 打包方法

### 方法1：安全打包（強烈推薦）
```bash
# 自動清理用戶數據並打包
build_exe.bat
```

### 方法2：手動安全打包
```bash
# 1. 清理用戶數據
clean_before_build.bat

# 2. 安裝 PyInstaller
pip install pyinstaller

# 3. 使用 spec 文件打包
pyinstaller facebook_tool.spec
```

### 方法2：簡單打包
```bash
# 運行簡單打包腳本
build_simple.bat
```

### 方法3：手動命令
```bash
# 基本打包命令
pyinstaller --onedir --console --add-data "templates;templates" app.py

# 單文件打包（較大但更方便）
pyinstaller --onefile --console --add-data "templates;templates" app.py
```

## 📁 打包後的文件結構

### 使用 Spec 文件打包後：
```
dist/
├── FacebookTool.exe           # 主程序
├── start_facebook_tool.bat    # 啟動器（自動打開瀏覽器）
└── _internal/                 # 依賴文件夾
    ├── templates/
    └── 其他依賴文件...
```

### 分發給其他電腦：
1. 將整個 `dist` 文件夾複製到目標電腦
2. 在目標電腦上運行 `start_facebook_tool.bat`

## ⚙️ 打包配置說明

### facebook_tool.spec 文件特點：
- **包含模板文件**：自動包含 `templates` 和 `static` 目錄
- **隱藏導入**：包含所有必需的 Python 模塊
- **排除不需要的包**：減小文件大小
- **控制台模式**：便於調試和查看日誌

### 自定義配置：
- **添加圖標**：在 spec 文件中設置 `icon='your_icon.ico'`
- **隱藏控制台**：將 `console=True` 改為 `console=False`
- **添加更多文件**：在 `datas` 列表中添加更多文件

## 🚀 使用打包後的程序

### 在目標電腦上：
1. **解壓縮**：將 dist 文件夾複製到目標位置
2. **運行**：雙擊 `start_facebook_tool.bat`
3. **等待**：程序啟動後會自動打開瀏覽器
4. **使用**：在 `http://localhost:5000` 使用工具

### 系統要求（目標電腦）：
- Windows 10/11（64位）
- Google Chrome 瀏覽器
- 網路連接
- **不需要安裝 Python**

## 🔧 故障排除

### 打包失敗：
1. **缺少模塊**：在 spec 文件的 `hiddenimports` 中添加缺少的模塊
2. **文件路徑錯誤**：檢查 `datas` 中的文件路徑是否正確
3. **權限問題**：以管理員身份運行打包命令

### 運行失敗：
1. **缺少 Chrome**：確保目標電腦已安裝 Chrome
2. **防毒軟體阻擋**：將程序添加到防毒軟體白名單
3. **端口占用**：確保 5000 端口未被占用

## 📊 文件大小優化

### 減小打包大小：
1. **使用 --exclude-module**：排除不需要的大型模塊
2. **使用 UPX 壓縮**：在 spec 文件中設置 `upx=True`
3. **移除調試信息**：設置 `debug=False`

### 預期文件大小：
- **單文件版本**：約 50-80 MB
- **目錄版本**：約 30-50 MB（分散在多個文件中）

## 🎯 最佳實踐

1. **測試打包**：在乾淨的虛擬機中測試打包後的程序
2. **版本控制**：為每個版本創建不同的打包
3. **文檔說明**：為用戶提供簡單的使用說明
4. **自動啟動**：使用啟動腳本自動打開瀏覽器

## 📝 分發清單

打包完成後，分發給用戶的文件：
- [ ] `dist` 文件夾（完整）
- [ ] `start_facebook_tool.bat`（啟動器）
- [ ] 使用說明文檔
- [ ] Chrome 瀏覽器安裝提醒
