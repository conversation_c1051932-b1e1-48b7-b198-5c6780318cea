# 📦 Facebook 工具打包清單

## 🎯 必需文件（必須複製）

### 核心程序文件
- [ ] `app.py` - 主要的Flask服務器
- [ ] `facebook_deleter_with_urls.py` - 貼文刪除功能
- [ ] `facebook_groups_crawler.py` - 社團抓取功能

### 配置文件
- [ ] `requirements.txt` - Python依賴列表
- [ ] `DEPLOYMENT_GUIDE.md` - 部署指南
- [ ] `check_environment.py` - 環境檢查腳本

### 啟動腳本
- [ ] `start_server.bat` - Windows啟動腳本（完整版）
- [ ] `start_simple.bat` - Windows啟動腳本（簡化版）
- [ ] `start_server.sh` - Linux/Mac啟動腳本（完整版）
- [ ] `start_simple.sh` - Linux/Mac啟動腳本（簡化版）

### 前端文件
- [ ] `templates/` 資料夾
  - [ ] `templates/index.html` - 主要前端界面

### 可選文件
- [ ] `static/` 資料夾（如果存在）
- [ ] `README.md` - 項目說明

## 🚫 不需要複製的文件

### 運行時生成的文件
- [ ] `__pycache__/` 資料夾
- [ ] `*.pyc` 文件
- [ ] `facebook_deleter.log` 日誌文件
- [ ] `urls.txt` 生成的URL文件
- [ ] `crawl_info.json` 抓取信息文件

### 開發文件
- [ ] `facebook_deleter_with_urls - 複製.py` 備份文件
- [ ] `.git/` 資料夾（如果存在）
- [ ] `.gitignore` 文件

## 📋 最小文件結構

```
facebook_tool/
├── app.py                              ✅ 必需
├── facebook_deleter_with_urls.py       ✅ 必需
├── facebook_groups_crawler.py          ✅ 必需
├── requirements.txt                    ✅ 必需
├── start_server.bat                    ✅ 必需 (Windows)
├── start_server.sh                     ✅ 必需 (Linux/Mac)
├── check_environment.py               ✅ 建議
├── DEPLOYMENT_GUIDE.md                ✅ 建議
├── templates/
│   └── index.html                      ✅ 必需
└── static/                             ⚪ 可選
```

## 🎯 快速打包步驟

### 方法1：手動複製
1. 在目標電腦創建新資料夾 `facebook_tool`
2. 按照上述清單複製所有必需文件
3. 保持資料夾結構不變

### 方法2：壓縮包
1. 選擇所有必需文件和資料夾
2. 創建 ZIP 或 RAR 壓縮包
3. 在目標電腦解壓縮

### 方法3：Git（如果使用版本控制）
```bash
git clone <repository_url>
cd facebook_tool
```

## ✅ 部署後檢查

1. **運行環境檢查**
   ```bash
   python check_environment.py
   ```

2. **手動檢查文件**
   - 確認所有必需文件都存在
   - 檢查 `templates/index.html` 是否完整

3. **測試啟動**
   - Windows: 雙擊 `start_server.bat`
   - Linux/Mac: 執行 `./start_server.sh`

## 🔧 故障排除

### 文件缺失
- 重新檢查打包清單
- 確保資料夾結構正確

### 權限問題（Linux/Mac）
```bash
chmod +x start_server.sh
chmod +x check_environment.py
```

### Python 路徑問題
- 確保 Python 已正確安裝
- 檢查 PATH 環境變數

## 📞 支援

如果遇到問題：
1. 首先運行 `python check_environment.py`
2. 查看 `DEPLOYMENT_GUIDE.md` 詳細說明
3. 檢查所有必需文件是否完整
