@echo off
echo Absolutely Clean Build Process...
echo.

echo Step 1: Kill all Python processes
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1
timeout /t 3 /nobreak >nul

echo Step 2: Complete file cleanup
REM Delete from current directory
for %%f in (urls.txt crawl_info.json facebook_deleter.log) do (
    if exist "%%f" (
        echo Deleting %%f
        del /f /q "%%f"
    )
)

REM Delete from temp directories
for %%d in ("%TEMP%" "%USERPROFILE%\AppData\Local\Temp") do (
    for %%f in (urls.txt crawl_info.json facebook_deleter.log) do (
        if exist "%%d\%%f" (
            echo Deleting %%d\%%f
            del /f /q "%%d\%%f"
        )
    )
)

REM Clean all cache directories
for %%d in (__pycache__ build dist .pytest_cache) do (
    if exist "%%d" (
        echo Removing %%d
        rmdir /s /q "%%d"
    )
)

REM Clean PyInstaller cache
if exist "%USERPROFILE%\.pyinstaller" rmdir /s /q "%USERPROFILE%\.pyinstaller"

echo Step 3: Create clean workspace
mkdir clean_build
cd clean_build

echo Step 4: Copy only essential files
copy ..\app.py .
copy ..\facebook_deleter_with_urls.py .
copy ..\facebook_groups_crawler.py .
copy ..\requirements.txt .
xcopy ..\templates templates\ /e /i
if exist ..\static xcopy ..\static static\ /e /i

echo Step 5: Install PyInstaller
pip install pyinstaller

echo Step 6: Build with absolute cleanliness
pyinstaller --onefile --console ^
    --exclude-module tkinter ^
    --exclude-module matplotlib ^
    --exclude-module numpy ^
    --exclude-module pandas ^
    --exclude-module PIL ^
    --exclude-module cv2 ^
    --add-data "templates;templates" ^
    --name FacebookTool_Clean ^
    app.py

echo Step 7: Verify and move result
if exist "dist\FacebookTool_Clean.exe" (
    echo Build successful!
    move "dist\FacebookTool_Clean.exe" "..\FacebookTool_Clean.exe"
    
    cd ..
    rmdir /s /q clean_build
    
    echo Step 8: Create launcher
    echo @echo off > start_clean_tool.bat
    echo echo Starting Clean Facebook Tool... >> start_clean_tool.bat
    echo start /b FacebookTool_Clean.exe >> start_clean_tool.bat
    echo timeout /t 3 /nobreak ^^^>nul >> start_clean_tool.bat
    echo start http://localhost:5000 >> start_clean_tool.bat
    echo pause >> start_clean_tool.bat
    
    echo Step 9: Final verification
    python check_exe_content.py FacebookTool_Clean.exe
    
    echo.
    echo ✅ Absolutely clean build completed!
    echo Executable: FacebookTool_Clean.exe
    echo Launcher: start_clean_tool.bat
    echo.
) else (
    echo ❌ Build failed!
    cd ..
    rmdir /s /q clean_build
)

pause
