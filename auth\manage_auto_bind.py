try:
    from .auth_system import auth_system
except ImportError:
    from auth_system import auth_system
import os

def toggle_auto_bind():
    """切換自動綁定狀態"""
    current_status = os.environ.get("AUTO_BIND_ENABLED", "true").lower() == "true"
    
    print(f"當前自動綁定狀態: {'啟用' if current_status else '禁用'}")
    
    new_status = not current_status
    new_value = "true" if new_status else "false"
    
    # 讀取 .env 文件
    env_file = ".env"
    lines = []
    
    try:
        with open(env_file, 'r') as f:
            lines = f.readlines()
    except FileNotFoundError:
        lines = []
    
    # 更新或添加 AUTO_BIND_ENABLED
    found = False
    for i, line in enumerate(lines):
        if line.startswith("AUTO_BIND_ENABLED="):
            lines[i] = f"AUTO_BIND_ENABLED={new_value}\n"
            found = True
            break
    
    if not found:
        lines.append(f"AUTO_BIND_ENABLED={new_value}\n")
    
    # 寫回文件
    with open(env_file, 'w') as f:
        f.writelines(lines)
    
    print(f"自動綁定已{'啟用' if new_status else '禁用'}")
    print("請重啟應用程序使設置生效")

def list_auto_bindings():
    """列出所有自動綁定的設備"""
    try:
        result = auth_system.supabase.table('device_authorizations').select('*').order('created_at', desc=True).execute()
        
        print("\n自動綁定設備列表:")
        print("-" * 80)
        
        auto_bindings = [d for d in result.data if d['device_name'].startswith('Auto-')]
        
        if not auto_bindings:
            print("沒有自動綁定的設備")
            return
        
        for device in auto_bindings:
            status = "有效" if device['is_active'] else "無效"
            expires = device.get('expires_at', '永久')
            if expires != '永久' and expires:
                from datetime import datetime
                expires = datetime.fromisoformat(expires.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"設備名稱: {device['device_name']}")
            print(f"設備ID: {device['device_id']}")
            print(f"用戶ID: {device['user_id']}")
            print(f"狀態: {status}")
            print(f"權限: {device.get('permissions', [])}")
            print(f"有效期: {expires}")
            print(f"綁定時間: {datetime.fromisoformat(device['authorized_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 80)
            
    except Exception as e:
        print(f"查詢失敗: {e}")

def revoke_device(device_id):
    """撤銷設備授權"""
    try:
        result = auth_system.supabase.table('device_authorizations').update({
            'is_active': False
        }).eq('device_id', device_id).execute()
        
        if result.data:
            print(f"設備 {device_id} 授權已撤銷")
        else:
            print(f"未找到設備 {device_id}")
            
    except Exception as e:
        print(f"撤銷失敗: {e}")

def main():
    print("自動綁定管理工具")
    print("=" * 50)
    
    while True:
        print(f"\n當前自動綁定狀態: {'啟用' if auth_system.is_auto_bind_enabled() else '禁用'}")
        print("\n選擇操作:")
        print("1. 切換自動綁定狀態")
        print("2. 列出自動綁定設備")
        print("3. 撤銷設備授權")
        print("4. 退出")
        
        choice = input("\n請選擇 (1-4): ").strip()
        
        if choice == "1":
            toggle_auto_bind()
            
        elif choice == "2":
            list_auto_bindings()
            
        elif choice == "3":
            device_id = input("請輸入要撤銷的設備ID: ").strip()
            if device_id:
                confirm = input(f"確定要撤銷設備 {device_id} 的授權嗎？(y/N): ").strip().lower()
                if confirm == 'y':
                    revoke_device(device_id)
            
        elif choice == "4":
            break
            
        else:
            print("無效選擇，請重試")

if __name__ == "__main__":
    main()
