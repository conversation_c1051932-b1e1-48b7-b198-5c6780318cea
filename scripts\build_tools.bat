@echo off
echo Facebook Tool - Build Tools
echo ============================

:menu
echo.
echo Select build method:
echo 1. Production Build (Recommended)
echo 2. Simple Build (PyInstaller onefile)
echo 3. cx_Freeze Build (Directory)
echo 4. Clean Development Environment
echo 5. Check Existing EXE
echo 6. Exit

set /p choice="Please choose (1-6): "

if "%choice%"=="1" (
    echo Starting production build...
    call build_production.bat
    goto menu
)

if "%choice%"=="2" (
    echo Starting simple build...
    call build_simple.bat
    goto menu
)

if "%choice%"=="3" (
    echo Starting cx_Freeze build...
    call build_cx_freeze.bat
    goto menu
)

if "%choice%"=="4" (
    echo Cleaning development environment...
    call clean_dev.bat
    goto menu
)

if "%choice%"=="5" (
    set /p exe_path="Enter EXE path to check: "
    if not "%exe_path%"=="" (
        python check_exe_content.py "%exe_path%"
        pause
    )
    goto menu
)

if "%choice%"=="6" (
    echo Goodbye!
    exit /b 0
)

echo Invalid choice, please try again.
goto menu
