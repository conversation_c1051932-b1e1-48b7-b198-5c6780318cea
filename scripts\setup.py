from cx_Freeze import setup, Executable
import sys
import os

# 包含的文件和目錄
include_files = [
    ("templates/", "templates/"),
]

# 如果 static 目錄存在，添加它
if os.path.exists("static"):
    include_files.append(("static/", "static/"))

# 需要包含的包
packages = [
    "flask",
    "flask_cors",
    "DrissionPage",
    "supabase",
    "dotenv",
    "threading",
    "time",
    "json",
    "logging",
    "os",
    "re",
    "hashlib",
    "platform",
    "uuid",
    "secrets",
    "string",
    "datetime"
]

# 構建選項
build_exe_options = {
    "packages": packages,
    "include_files": include_files,
    "excludes": ["tkinter"],  # 排除不需要的包
}

# 可執行文件設置
executables = [
    Executable(
        "app.py",
        base="Console",  # 使用 "Win32GUI" 可以隱藏控制台
        target_name="FacebookTool.exe",
        icon=None  # 可以添加圖標文件路徑
    )
]

setup(
    name="Facebook Tool",
    version="1.0",
    description="Facebook Groups and Posts Management Tool",
    options={"build_exe": build_exe_options},
    executables=executables
)
