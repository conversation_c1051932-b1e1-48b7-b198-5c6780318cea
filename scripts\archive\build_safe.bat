@echo off
echo Building Facebook Tool...

echo Step 1: Cleaning user data
if exist "urls.txt" del "urls.txt"
if exist "crawl_info.json" del "crawl_info.json"
if exist "facebook_deleter.log" del "facebook_deleter.log"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

echo Step 2: Installing PyInstaller
pip install pyinstaller

echo Step 3: Building executable
pyinstaller facebook_tool.spec

echo Step 4: Creating launcher
echo @echo off > dist\start_facebook_tool.bat
echo echo Starting Facebook Tool... >> dist\start_facebook_tool.bat
echo start /b FacebookTool.exe >> dist\start_facebook_tool.bat
echo timeout /t 3 /nobreak ^>nul >> dist\start_facebook_tool.bat
echo start http://localhost:5000 >> dist\start_facebook_tool.bat
echo echo Tool is running. Close this window to stop. >> dist\start_facebook_tool.bat
echo pause >> dist\start_facebook_tool.bat

echo.
echo Build complete!
echo Executable: dist\FacebookTool.exe
echo Launcher: dist\start_facebook_tool.bat
echo.
pause
