#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理Supabase資料庫中的測試數據
移除所有設備綁定、使用日誌和授權碼記錄
"""

try:
    from .auth_system import auth_system
except ImportError:
    from auth_system import auth_system
from datetime import datetime

def list_all_data():
    """列出所有資料庫數據"""
    print("📊 當前資料庫內容:")
    print("=" * 80)
    
    try:
        # 查看設備授權
        devices = auth_system.supabase.table('device_authorizations').select('*').execute()
        print(f"\n🔐 設備授權記錄 ({len(devices.data)} 條):")
        print("-" * 80)
        for device in devices.data:
            print(f"設備ID: {device['device_id']}")
            print(f"Facebook ID: {device['user_id']}")
            print(f"設備名稱: {device['device_name']}")
            print(f"狀態: {'有效' if device['is_active'] else '無效'}")
            print(f"綁定時間: {device['authorized_at']}")
            print("-" * 40)
        
        # 查看授權碼
        codes = auth_system.supabase.table('auth_codes').select('*').execute()
        print(f"\n🎫 授權碼記錄 ({len(codes.data)} 條):")
        print("-" * 80)
        for code in codes.data:
            print(f"授權碼: {code['code']}")
            print(f"用戶ID: {code['user_id']}")
            print(f"使用槽位: {code['used_slots']}/{code['device_slots']}")
            print(f"狀態: {'有效' if code['is_active'] else '無效'}")
            print(f"創建時間: {code['created_at']}")
            print("-" * 40)
        
        # 查看使用日誌
        logs = auth_system.supabase.table('usage_logs').select('*').order('timestamp', desc=True).limit(10).execute()
        print(f"\n📝 使用日誌 (最近10條，共查詢到 {len(logs.data)} 條):")
        print("-" * 80)
        for log in logs.data:
            print(f"時間: {log['timestamp']}")
            print(f"Facebook ID: {log['user_id']}")
            print(f"設備ID: {log['device_id']}")
            print(f"操作: {log['action']}")
            print("-" * 40)
            
    except Exception as e:
        print(f"❌ 查詢失敗: {e}")

def clean_device_authorizations():
    """清理所有設備授權記錄"""
    try:
        result = auth_system.supabase.table('device_authorizations').delete().neq('id', 0).execute()
        print(f"✅ 已清理 {len(result.data)} 條設備授權記錄")
        return True
    except Exception as e:
        print(f"❌ 清理設備授權失敗: {e}")
        return False

def clean_auth_codes():
    """清理所有授權碼記錄"""
    try:
        result = auth_system.supabase.table('auth_codes').delete().neq('id', 0).execute()
        print(f"✅ 已清理 {len(result.data)} 條授權碼記錄")
        return True
    except Exception as e:
        print(f"❌ 清理授權碼失敗: {e}")
        return False

def clean_usage_logs():
    """清理所有使用日誌"""
    try:
        result = auth_system.supabase.table('usage_logs').delete().neq('id', 0).execute()
        print(f"✅ 已清理 {len(result.data)} 條使用日誌")
        return True
    except Exception as e:
        print(f"❌ 清理使用日誌失敗: {e}")
        return False

def clean_specific_device(device_id):
    """清理特定設備的所有記錄"""
    try:
        # 清理設備授權
        device_result = auth_system.supabase.table('device_authorizations').delete().eq('device_id', device_id).execute()
        
        # 清理使用日誌
        log_result = auth_system.supabase.table('usage_logs').delete().eq('device_id', device_id).execute()
        
        print(f"✅ 已清理設備 {device_id}:")
        print(f"   - 設備授權: {len(device_result.data)} 條")
        print(f"   - 使用日誌: {len(log_result.data)} 條")
        return True
        
    except Exception as e:
        print(f"❌ 清理設備記錄失敗: {e}")
        return False

def clean_all_data():
    """清理所有測試數據"""
    print("⚠️ 即將清理所有資料庫數據！")
    print("這將刪除:")
    print("- 所有設備綁定記錄")
    print("- 所有授權碼記錄") 
    print("- 所有使用日誌")
    
    confirm = input("\n確定要繼續嗎？(輸入 'YES' 確認): ").strip()
    
    if confirm != "YES":
        print("操作已取消")
        return False
    
    print("\n🧹 開始清理...")
    
    success = True
    success &= clean_usage_logs()
    success &= clean_device_authorizations()
    success &= clean_auth_codes()
    
    if success:
        print("\n🎉 所有測試數據已清理完成！")
        print("現在可以安全打包，不會包含任何測試數據。")
    else:
        print("\n❌ 清理過程中出現錯誤，請檢查並重試。")
    
    return success

def main():
    print("Supabase 資料庫清理工具")
    print("=" * 50)
    print("清理測試數據，確保打包安全")
    
    while True:
        print(f"\n選擇操作:")
        print("1. 查看所有數據")
        print("2. 清理當前設備記錄")
        print("3. 清理特定設備記錄")
        print("4. 清理所有數據 (危險操作)")
        print("5. 退出")
        
        choice = input("\n請選擇 (1-5): ").strip()
        
        if choice == "1":
            list_all_data()
            
        elif choice == "2":
            device_id = auth_system.get_device_id()
            print(f"當前設備ID: {device_id}")
            confirm = input("確定要清理當前設備的所有記錄嗎？(y/N): ").strip().lower()
            if confirm == 'y':
                clean_specific_device(device_id)
            
        elif choice == "3":
            device_id = input("請輸入要清理的設備ID: ").strip()
            if device_id:
                confirm = input(f"確定要清理設備 {device_id} 的所有記錄嗎？(y/N): ").strip().lower()
                if confirm == 'y':
                    clean_specific_device(device_id)
            
        elif choice == "4":
            clean_all_data()
            
        elif choice == "5":
            break
            
        else:
            print("無效選擇，請重試")

if __name__ == "__main__":
    main()
