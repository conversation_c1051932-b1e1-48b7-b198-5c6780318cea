# 🔒 安全打包指南

## ⚠️ 重要警告

**在打包前必須清理用戶數據，避免洩露個人隱私！**

## 🗂️ 包含用戶信息的文件

以下文件包含用戶的個人信息，**絕對不能**包含在打包中：

### 📋 運行時生成的文件
- `urls.txt` - 包含用戶ID的社團URL列表
- `crawl_info.json` - 包含用戶ID和抓取信息
- `facebook_deleter.log` - 包含操作日誌和用戶信息

### 🗃️ 系統文件
- `__pycache__/` - Python緩存目錄
- `*.pyc` - Python編譯文件
- `build/` - 構建目錄
- `dist/` - 分發目錄

## 🧹 安全打包步驟

### 方法1：徹底清理打包（強烈推薦）
```bash
# 徹底清理並驗證的安全打包
build_clean.bat
```

### 方法2：手動徹底清理
```bash
# 1. 徹底清理所有位置的用戶數據
clean_thorough.bat

# 2. 打包
pyinstaller --clean --noconfirm facebook_tool.spec

# 3. 驗證結果
verify_build.bat
```

### 方法3：簡單清理（可能不完整）
```bash
# 1. 簡單清理
clean_simple.bat

# 2. 打包
pyinstaller facebook_tool.spec

# 3. 驗證
verify_build.bat
```

### 方法3：完全手動清理
```bash
# 刪除用戶數據文件
del urls.txt
del crawl_info.json
del facebook_deleter.log

# 清理緩存
rmdir /s /q __pycache__
rmdir /s /q build
rmdir /s /q dist

# 打包
pyinstaller facebook_tool.spec
```

## ✅ 安全檢查清單

打包前請確認：

- [ ] 已刪除 `urls.txt`
- [ ] 已刪除 `crawl_info.json`
- [ ] 已刪除 `facebook_deleter.log`
- [ ] 已清理 `__pycache__` 目錄
- [ ] 已清理舊的 `build` 和 `dist` 目錄
- [ ] 沒有其他包含用戶ID的文件

## 🔍 驗證打包結果

### 自動驗證（推薦）
```bash
# 運行自動驗證腳本
verify_build.bat
```

### 手動驗證
打包完成後，檢查 `dist` 目錄：

1. **檢查文件**：
   ```bash
   # 檢查是否包含用戶數據文件
   dir dist\FacebookTool\_internal\
   ```

2. **搜索用戶ID**：
   ```bash
   # 在打包結果中搜索用戶ID模式
   findstr /s /c:"100000" dist\*.*
   ```

3. **檢查特定文件**：
   - 確保沒有 `urls.txt`
   - 確保沒有 `crawl_info.json`
   - 確保沒有 `facebook_deleter.log`

## 🚨 如果發現用戶數據

如果在打包結果中發現用戶數據：

1. **立即停止分發**
2. **重新清理並打包**
3. **檢查清理腳本是否正確執行**

## 💡 最佳實踐

### 開發環境分離
- 使用專門的乾淨環境進行打包
- 不要在有用戶數據的環境中打包

### 自動化清理
- 始終使用 `clean_before_build.bat` 腳本
- 定期檢查是否有新的用戶數據文件

### 版本控制
- 將用戶數據文件添加到 `.gitignore`
- 確保不會意外提交用戶數據

## 📝 .gitignore 建議

```gitignore
# 用戶數據文件
urls.txt
crawl_info.json
facebook_deleter.log
*.log

# Python 緩存
__pycache__/
*.pyc
*.pyo

# 構建目錄
build/
dist/
*.spec.bak
```

## 🔧 故障排除

### 如果清理腳本失敗
1. 手動檢查並刪除用戶數據文件
2. 確保沒有程序正在使用這些文件
3. 以管理員身份運行清理腳本

### 如果仍然包含用戶數據
1. 檢查 PyInstaller 的 spec 文件配置
2. 確認 `datas` 部分沒有包含用戶數據目錄
3. 使用 `--exclude` 參數明確排除文件

## 📞 支援

如果遇到問題：
1. 檢查清理腳本是否正確執行
2. 驗證 spec 文件配置
3. 確認沒有硬編碼的用戶信息
