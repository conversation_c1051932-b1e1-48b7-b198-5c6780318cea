try:
    from .auth_system import auth_system
except ImportError:
    from auth_system import auth_system
from datetime import datetime, timedelta
import secrets
import string

def generate_auth_code():
    """生成授權碼"""
    return ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(12))

def create_auth_code(device_slots: int = 1, days_valid: int = 30, description: str = ""):
    """創建授權碼 - 不預設用戶ID，使用時自動綁定Facebook ID"""
    code = generate_auth_code()
    expires_at = datetime.now() + timedelta(days=days_valid)

    try:
        result = auth_system.supabase.table('auth_codes').insert({
            'code': code,
            'user_id': '',  # 空白，使用時自動填入Facebook ID
            'device_slots': device_slots,
            'expires_at': expires_at.isoformat(),
            'is_active': True
        }).execute()

        print(f"授權碼創建成功:")
        print(f"授權碼: {code}")
        print(f"描述: {description or '無'}")
        print(f"設備槽位: {device_slots}")
        print(f"有效期至: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"使用說明: 用戶輸入此授權碼後，系統會自動抓取並綁定其Facebook ID")

        return code

    except Exception as e:
        print(f"創建失敗: {e}")
        return None

def list_auth_codes():
    """列出所有授權碼"""
    try:
        result = auth_system.supabase.table('auth_codes').select('*').order('created_at', desc=True).execute()
        
        print("\n所有授權碼:")
        print("-" * 80)
        for code in result.data:
            status = "有效" if code['is_active'] else "無效"
            expires = code.get('expires_at', '永久')
            if expires != '永久':
                expires = datetime.fromisoformat(expires.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"授權碼: {code['code']}")
            print(f"用戶ID: {code['user_id']}")
            print(f"狀態: {status}")
            print(f"設備槽位: {code['used_slots']}/{code['device_slots']}")
            print(f"有效期: {expires}")
            print(f"創建時間: {datetime.fromisoformat(code['created_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 80)
            
    except Exception as e:
        print(f"查詢失敗: {e}")

def list_devices():
    """列出所有授權設備"""
    try:
        result = auth_system.supabase.table('device_authorizations').select('*').order('created_at', desc=True).execute()
        
        print("\n所有授權設備:")
        print("-" * 80)
        for device in result.data:
            status = "有效" if device['is_active'] else "無效"
            expires = device.get('expires_at', '永久')
            if expires != '永久':
                expires = datetime.fromisoformat(expires.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"設備名稱: {device['device_name']}")
            print(f"設備ID: {device['device_id']}")
            print(f"用戶ID: {device['user_id']}")
            print(f"狀態: {status}")
            print(f"有效期: {expires}")
            print(f"授權時間: {datetime.fromisoformat(device['authorized_at'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 80)
            
    except Exception as e:
        print(f"查詢失敗: {e}")

if __name__ == "__main__":
    print("Facebook Tool 授權管理")
    print("=" * 50)
    
    while True:
        print("\n選擇操作:")
        print("1. 創建授權碼")
        print("2. 列出授權碼")
        print("3. 列出授權設備")
        print("4. 退出")
        
        choice = input("\n請選擇 (1-4): ").strip()
        
        if choice == "1":
            print("\n創建新授權碼:")
            description = input("授權碼描述 (可選): ").strip()

            device_slots = input("設備槽位數量 (默認1): ").strip()
            device_slots = int(device_slots) if device_slots else 1

            days_valid = input("有效天數 (默認30): ").strip()
            days_valid = int(days_valid) if days_valid else 30

            create_auth_code(device_slots, days_valid, description)
            
        elif choice == "2":
            list_auth_codes()
            
        elif choice == "3":
            list_devices()
            
        elif choice == "4":
            break
            
        else:
            print("無效選擇，請重試")
