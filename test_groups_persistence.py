#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試社團數據持久化功能
"""

import requests
import json
import os

def test_groups_persistence():
    """測試社團數據持久化"""
    print("測試社團數據持久化...")
    print("=" * 50)
    
    try:
        # 檢查是否存在 crawl_info.json 文件
        if os.path.exists('crawl_info.json'):
            print("✅ 找到 crawl_info.json 文件")
            
            # 讀取文件內容
            with open('crawl_info.json', 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            print(f"📂 文件中的數據:")
            print(f"   - 用戶ID: {file_data.get('user_id', '無')}")
            print(f"   - 社團數量: {len(file_data.get('groups', []))}")
            
            if file_data.get('groups'):
                print(f"   - 前3個社團:")
                for i, group in enumerate(file_data['groups'][:3]):
                    print(f"     {i+1}. {group.get('name', '無名稱')} ({group.get('id', '無ID')})")
        else:
            print("❌ 沒有找到 crawl_info.json 文件")
            print("💡 請先運行抓取功能生成社團數據")
            return False
        
        # 測試API是否能正確載入
        print("\n🔄 測試API載入...")
        response = requests.get('http://localhost:5000/api/groups')
        
        if response.status_code == 200:
            api_data = response.json()
            print("✅ API響應成功")
            print(f"   - 用戶ID: {api_data.get('user_id', '無')}")
            print(f"   - 社團數量: {api_data.get('total_groups', 0)}")
            
            if api_data.get('total_groups', 0) > 0:
                print("🎉 社團數據持久化功能正常！")
                return True
            else:
                print("❌ API沒有返回社團數據")
                return False
        else:
            print(f"❌ API請求失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    test_groups_persistence()
