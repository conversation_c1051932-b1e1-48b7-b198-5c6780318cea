<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Auto</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: headerGlow 6s ease-in-out infinite alternate;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 15px;
            font-weight: 300;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            opacity: 0.95;
            font-size: 1.2rem;
            position: relative;
            z-index: 1;
            font-weight: 300;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 30px;
            padding: 35px;
            border: 1px solid rgba(0,0,0,0.08);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .section:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.1);
            border-color: rgba(79, 172, 254, 0.2);
        }

        .section:hover::before {
            opacity: 1;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 1.9rem;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            position: relative;
        }

        .section h2::after {
            content: '';
            flex: 1;
            height: 1px;
            background: linear-gradient(90deg, rgba(79, 172, 254, 0.3) 0%, transparent 100%);
            margin-left: 20px;
        }

        .section p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-container {
            margin-top: 25px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(79, 172, 254, 0.2);
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(233, 236, 239, 0.8);
            border-radius: 20px;
            overflow: hidden;
            margin-bottom: 15px;
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
            animation: shimmer 2s infinite;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 50%, #4facfe 100%);
            background-size: 200% 100%;
            animation: gradientMove 3s ease-in-out infinite;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            width: 0%;
            border-radius: 20px;
            position: relative;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @keyframes gradientMove {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .progress-text {
            text-align: center;
            color: #666;
            font-weight: 500;
        }

        .alert {
            padding: 20px;
            border-radius: 16px;
            margin-bottom: 25px;
            position: relative;
            backdrop-filter: blur(10px);
            border: 1px solid;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 4px solid;
        }

        .alert-info {
            background: rgba(209, 236, 241, 0.9);
            border-color: rgba(23, 162, 184, 0.8);
            color: #0c5460;
        }

        .alert-success {
            background: rgba(212, 237, 218, 0.9);
            border-color: rgba(40, 167, 69, 0.8);
            color: #155724;
        }

        .alert-warning {
            background: rgba(255, 243, 205, 0.9);
            border-color: rgba(255, 193, 7, 0.8);
            color: #8b5a00;
        }

        .alert-danger {
            background: rgba(248, 215, 218, 0.9);
            border-color: rgba(220, 53, 69, 0.8);
            color: #721c24;
        }

        .alert strong {
            font-weight: 600;
        }

        .alert ul {
            margin: 0;
            padding-left: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .file-download {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            padding: 16px 24px;
            background: rgba(248, 249, 250, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(222, 226, 230, 0.8);
            border-radius: 14px;
            text-decoration: none;
            color: #495057;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-right: 15px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .file-download::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.1), transparent);
            transition: left 0.5s;
        }

        .file-download:hover {
            background: rgba(233, 236, 239, 0.9);
            text-decoration: none;
            color: #495057;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: rgba(79, 172, 254, 0.3);
        }

        .file-download:hover::before {
            left: 100%;
        }

        .file-download.available {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-idle { background: #6c757d; }
        .status-running { background: #007bff; animation: pulse 1.5s infinite; }
        .status-completed { background: #28a745; }
        .status-error { background: #dc3545; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @keyframes headerGlow {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section {
            animation: slideInUp 0.6s ease-out;
        }

        .section:nth-child(1) { animation-delay: 0.1s; }
        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.3s; }
        .section:nth-child(4) { animation-delay: 0.4s; }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                margin: 0;
                border-radius: 20px;
            }

            .content {
                padding: 25px;
            }

            .section {
                padding: 25px;
                margin-bottom: 25px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .header h1 span {
                font-size: 2.5rem !important;
                margin-right: 10px;
            }

            .button-group {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                width: 100%;
                justify-content: center;
                padding: 16px 24px;
            }

            .file-download {
                width: 100%;
                margin-right: 0;
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <span style="font-size: 3rem; margin-right: 15px; display: inline-block; animation: bounce 2s infinite;">🚀</span>
                Facebook Auto
            </h1>
        </div>

        <div class="content">
            <!-- 狀態顯示 -->
            <div class="section">
                <h2>📊 系統狀態</h2>
                <div id="statusDisplay">
                    <p><span class="status-indicator status-idle"></span>系統待機中...</p>
                </div>
            </div>

            <!-- 瀏覽器狀態檢查 -->
            <div class="section">
                <h2>🌐 瀏覽器狀態檢查</h2>
                <p style="margin-bottom: 20px; color: #6c757d;">檢查Chrome瀏覽器連接和Facebook登入狀態</p>

                <div class="alert alert-info" style="margin-bottom: 20px;">
                    <strong>💡 提示：</strong>
                    <ul style="margin-left: 20px; margin-top: 5px;">
                        <li>點擊檢查會開啟Chrome瀏覽器（如果尚未開啟）</li>
                        <li>如果未登入Facebook，會自動導航到Facebook登入頁面</li>
                        <li>瀏覽器會保持開啟，您可以在其中登入Facebook</li>
                        <li>🔄 系統會自動監控登入狀態，無需手動重新檢查</li>
                    </ul>
                </div>

                <div class="button-group" style="margin-bottom: 20px;">
                    <button class="btn btn-info" id="checkBrowser">🔍 檢查瀏覽器狀態</button>
                    <button class="btn btn-secondary" id="stopMonitoring" style="display: none;">⏹️ 停止監控</button>
                </div>

                <div class="alert alert-info" style="margin-bottom: 20px;">
                    <strong>🔄 自動檢查中...</strong>
                    <p>系統會自動檢查瀏覽器狀態，您也可以手動點擊「檢查瀏覽器狀態」按鈕</p>
                </div>

                <div id="browserStatus" style="display: none;">
                    <!-- 瀏覽器狀態將在這裡顯示 -->
                </div>
            </div>

            <!-- 社團抓取 -->
            <div class="section">
                <h2>🕷️ 社團管理</h2>
                <p>抓取Facebook社團並選擇要處理的社團</p>

                <div class="button-group">
                    <button class="btn btn-primary" id="startCrawler">🚀 抓取社團列表</button>
                    <button class="btn btn-secondary" id="stopCrawler" disabled>停止抓取</button>
                    <button class="btn btn-info" id="refreshGroups">🔄 刷新社團列表</button>
                </div>

                <div class="progress-container" id="crawlerProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="crawlerProgressFill"></div>
                    </div>
                    <div class="progress-text" id="crawlerProgressText">準備中...</div>
                </div>

                <!-- 社團列表 -->
                <div id="groupsList" style="display: none; margin-top: 25px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3 style="margin: 0; color: #2c3e50;">📋 社團列表</h3>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-secondary" id="selectAllGroups" style="padding: 8px 16px; font-size: 0.9rem;">全選</button>
                            <button class="btn btn-secondary" id="deselectAllGroups" style="padding: 8px 16px; font-size: 0.9rem;">全不選</button>
                        </div>
                    </div>

                    <div id="userInfo" style="margin-bottom: 15px; padding: 12px; background: rgba(79, 172, 254, 0.1); border-radius: 8px; font-size: 0.9rem;">
                        <strong>👤 用戶ID：</strong><span id="userIdDisplay">-</span>
                    </div>

                    <div id="groupsContainer" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px; padding: 10px;">
                        <!-- 社團列表將在這裡動態生成 -->
                    </div>

                    <div style="margin-top: 15px; padding: 12px; background: rgba(40, 167, 69, 0.1); border-radius: 8px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span><strong>✅ 已選擇：</strong><span id="selectedCount">0</span> 個社團 <small style="color: #666;">(自動確認)</small></span>
                            <button class="btn btn-primary" id="confirmSelection" disabled style="display: none; padding: 8px 16px; font-size: 0.9rem;">確認選擇</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 貼文刪除 -->
            <div class="section">
                <h2>🗑️ 貼文刪除功能</h2>
                <p style="margin-bottom: 25px; color: #6c757d;">選擇刪除模式：全部刪除或針對性刪除</p>

                <!-- 刪除模式選擇 -->
                <div style="margin-bottom: 25px;">
                    <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="deleteMode" value="all" checked style="margin: 0;">
                            <span style="font-weight: 500;">🗑️ 全部刪除模式</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="deleteMode" value="tags" style="margin: 0;">
                            <span style="font-weight: 500;">🎯 針對性刪除模式</span>
                        </label>
                    </div>
                </div>

                <!-- 全部刪除說明 -->
                <div id="allDeleteInfo" class="alert alert-warning">
                    <strong>⚠️ 全部刪除模式：</strong>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>此功能會刪除您在所有社團中的<strong>所有可見貼文</strong></li>
                        <li>刪除操作<strong>不可逆</strong>，請謹慎使用</li>
                        <li>建議在重要資料已備份的情況下使用</li>
                    </ul>
                </div>

                <!-- 針對性刪除設定 -->
                <div id="tagDeleteInfo" class="alert alert-info" style="display: none;">
                    <strong>🎯 針對性刪除模式：</strong>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>只刪除包含<strong>所有指定標籤</strong>的貼文</li>
                        <li>支援多個標籤組合，需同時包含才會刪除</li>
                        <li>標籤格式：可以有或沒有 # 符號</li>
                    </ul>

                    <div style="margin-top: 15px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">
                            🏷️ 目標標籤（每行一個）：
                        </label>
                        <textarea id="targetTags" placeholder="例如：&#10;苑裡市區建地&#10;都市計畫內&#10;#房地產"
                                  style="width: 100%; height: 100px; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-family: inherit; resize: vertical;"></textarea>
                        <small style="color: #666; margin-top: 5px; display: block;">
                            💡 提示：每行輸入一個標籤，只有同時包含所有標籤的貼文才會被刪除
                        </small>
                    </div>
                </div>

                <div class="button-group" style="margin-top: 20px;">
                    <button class="btn btn-danger" id="startDeleter">🗑️ 開始刪除</button>
                    <button class="btn btn-secondary" id="stopDeleter" disabled>停止刪除</button>
                </div>

                <div class="progress-container" id="deleterProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="deleterProgressFill"></div>
                    </div>
                    <div class="progress-text" id="deleterProgressText">準備中...</div>
                </div>
            </div>


        </div>
    </div>

    <script>
        // DOM 元素
        const elements = {
            startCrawler: document.getElementById('startCrawler'),
            stopCrawler: document.getElementById('stopCrawler'),
            refreshGroups: document.getElementById('refreshGroups'),
            startDeleter: document.getElementById('startDeleter'),
            stopDeleter: document.getElementById('stopDeleter'),
            statusDisplay: document.getElementById('statusDisplay'),
            crawlerProgress: document.getElementById('crawlerProgress'),
            crawlerProgressFill: document.getElementById('crawlerProgressFill'),
            crawlerProgressText: document.getElementById('crawlerProgressText'),
            deleterProgress: document.getElementById('deleterProgress'),
            deleterProgressFill: document.getElementById('deleterProgressFill'),
            deleterProgressText: document.getElementById('deleterProgressText'),
            allDeleteInfo: document.getElementById('allDeleteInfo'),
            tagDeleteInfo: document.getElementById('tagDeleteInfo'),
            targetTags: document.getElementById('targetTags'),
            groupsList: document.getElementById('groupsList'),
            groupsContainer: document.getElementById('groupsContainer'),
            userIdDisplay: document.getElementById('userIdDisplay'),
            selectedCount: document.getElementById('selectedCount'),
            selectAllGroups: document.getElementById('selectAllGroups'),
            deselectAllGroups: document.getElementById('deselectAllGroups'),
            confirmSelection: document.getElementById('confirmSelection'),
            checkBrowser: document.getElementById('checkBrowser'),
            stopMonitoring: document.getElementById('stopMonitoring'),
            browserStatus: document.getElementById('browserStatus')
        };

        // 社團列表管理
        let groupsData = [];
        let selectedGroups = [];

        // 瀏覽器狀態檢查
        let browserCheckInterval = null;
        let lastLoginStatus = null;

        async function checkBrowserStatus(isAutoCheck = false) {
            try {
                if (!isAutoCheck) {
                    elements.checkBrowser.disabled = true;
                    elements.checkBrowser.textContent = '🔄 檢查中...';
                }

                const response = await apiCall('/api/browser/check', {
                    method: 'POST'
                });

                displayBrowserStatus(response);

                // 如果是首次檢查且未登入，開始自動監控
                if (!isAutoCheck && response.browser_connected && !response.login_status) {
                    console.log('🔍 檢測到瀏覽器已連接但未登入，開始監控...');
                    startBrowserMonitoring();
                }

                // 如果登入狀態從未登入變為已登入，停止自動監控
                if (response.login_status && (lastLoginStatus === false || lastLoginStatus === null)) {
                    console.log('🎉 檢測到登入狀態變化：', lastLoginStatus, '->', response.login_status);
                    stopBrowserMonitoring();
                    showAlert('🎉 檢測到Facebook登入成功！用戶ID: ' + (response.user_id || '未知'), 'success');

                    // 立即更新顯示狀態
                    displayBrowserStatus(response);
                }

                // 調試信息
                if (isAutoCheck) {
                    console.log(`🔄 自動檢查結果：瀏覽器連接=${response.browser_connected}, 登入狀態=${response.login_status}, 上次狀態=${lastLoginStatus}`);
                }

                lastLoginStatus = response.login_status;

            } catch (error) {
                displayBrowserStatus({
                    success: false,
                    browser_connected: false,
                    error: error.message,
                    message: '檢查失敗'
                });
            } finally {
                if (!isAutoCheck) {
                    elements.checkBrowser.disabled = false;
                    elements.checkBrowser.textContent = '🔍 檢查瀏覽器狀態';
                }
            }
        }

        // 開始瀏覽器狀態監控
        function startBrowserMonitoring() {
            if (browserCheckInterval) {
                console.log('監控已在運行中，跳過啟動');
                return; // 避免重複啟動
            }

            console.log('🔄 開始監控瀏覽器登入狀態...');
            showAlert('🔄 開始自動監控Facebook登入狀態...', 'info');

            browserCheckInterval = setInterval(() => {
                console.log('⏱️ 自動檢查瀏覽器狀態...');
                checkBrowserStatus(true); // 自動檢查
            }, 2000); // 每2秒檢查一次，更快響應

            // 更新按鈕狀態
            elements.checkBrowser.textContent = '🔄 監控登入狀態中...';
            elements.checkBrowser.style.background = '#17a2b8';
            elements.checkBrowser.style.color = 'white';
            elements.stopMonitoring.style.display = 'inline-block';
        }

        // 停止瀏覽器狀態監控
        function stopBrowserMonitoring() {
            if (browserCheckInterval) {
                clearInterval(browserCheckInterval);
                browserCheckInterval = null;
                console.log('⏹️ 停止監控瀏覽器登入狀態');

                // 恢復按鈕狀態
                elements.checkBrowser.textContent = '🔍 檢查瀏覽器狀態';
                elements.checkBrowser.style.background = '';
                elements.checkBrowser.style.color = '';
                elements.stopMonitoring.style.display = 'none';
            }
        }

        // 顯示瀏覽器狀態
        function displayBrowserStatus(status) {
            const container = elements.browserStatus;
            container.style.display = 'block';

            let statusClass = 'alert-info';
            let statusIcon = '🔍';
            let statusTitle = '瀏覽器狀態';

            if (status.success && status.browser_connected) {
                if (status.login_status && status.authorized) {
                    statusClass = 'alert-success';
                    statusIcon = '✅';
                    statusTitle = '已授權';
                } else if (status.login_status && !status.authorized) {
                    statusClass = 'alert-danger';
                    statusIcon = '❌';
                    statusTitle = '需要授權';
                } else {
                    statusClass = 'alert-warning';
                    statusIcon = '⚠️';
                    statusTitle = '需要登入';
                }
            } else {
                statusClass = 'alert-danger';
                statusIcon = '❌';
                statusTitle = '連接失敗';
            }

            let html = `
                <div class="alert ${statusClass}">
                    <h4>${statusIcon} ${statusTitle}</h4>
                    <p><strong>訊息：</strong>${status.message || '無訊息'}</p>
            `;

            if (status.browser_connected) {
                html += `
                    <div style="margin-top: 15px;">
                        <p><strong>🌐 當前頁面：</strong>${status.current_url || '未知'}</p>
                        <p><strong>📄 頁面標題：</strong>${status.page_title || '未知'}</p>
                        <p><strong>🏠 Facebook域名：</strong>${status.is_facebook ? '是' : '否'}</p>
                        <p><strong>👤 登入狀態：</strong>${status.login_status ? '已登入' : '未登入'}</p>
                `;

                if (status.user_id) {
                    html += `<p><strong>🆔 用戶ID：</strong>${status.user_id}</p>`;
                }

                if (status.device_id) {
                    html += `<p><strong>🔧 設備ID：</strong>${status.device_id}</p>`;
                }

                if (status.login_status) {
                    html += `<p><strong>🔐 授權狀態：</strong>${status.authorized ? '已授權' : '未授權'}</p>`;

                    if (status.auto_bind_enabled) {
                        html += `<p><strong>🔄 自動綁定：</strong>已啟用</p>`;
                    }
                }

                html += `</div>`;
            }

            if (status.error) {
                html += `<p style="margin-top: 10px;"><strong>錯誤詳情：</strong>${status.error}</p>`;
            }

            // 添加建議
            if (!status.browser_connected) {
                html += `
                    <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                        <strong>💡 解決建議：</strong>
                        <ul style="margin: 5px 0 0 20px;">
                            <li>確保Chrome瀏覽器已開啟</li>
                            <li>嘗試重新啟動Chrome瀏覽器</li>
                            <li>檢查是否有其他程序占用瀏覽器</li>
                        </ul>
                    </div>
                `;
            } else if (!status.login_status) {
                html += `
                    <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                        <strong>💡 下一步操作：</strong>
                        <ul style="margin: 5px 0 0 20px;">
                            <li>瀏覽器已開啟並導航到Facebook</li>
                            <li>請在瀏覽器中登入您的Facebook帳號</li>
                            <li>🔄 系統會自動監控登入狀態（每3秒檢查一次）</li>
                            <li>登入成功後會自動顯示通知</li>
                        </ul>
                        <div style="margin-top: 10px; padding: 8px; background: rgba(23, 162, 184, 0.2); border-radius: 6px; font-size: 0.9rem;">
                            <strong>⏱️ 自動監控：</strong>正在等待Facebook登入...
                            <span style="animation: pulse 1.5s infinite;">🔄</span>
                        </div>
                    </div>
                `;
            } else if (status.login_status && !status.authorized) {
                // 檢查拒絕原因
                if (status.auth_message && status.auth_message.includes('已綁定到Facebook ID')) {
                    // 設備已綁定到其他Facebook帳號
                    html += `
                        <div style="margin-top: 15px; padding: 10px; background: rgba(220, 53, 69, 0.2); border-radius: 8px; border: 2px solid #dc3545;">
                            <strong>🚫 訪問被拒絕：</strong>
                            <p style="margin: 5px 0; color: #721c24;">${status.auth_message}</p>
                            <div style="margin-top: 15px; padding: 10px; background: rgba(255, 255, 255, 0.8); border-radius: 6px;">
                                <strong>🔒 安全說明：</strong>
                                <ul style="margin: 5px 0 0 20px; color: #721c24;">
                                    <li>此設備已與特定Facebook帳號綁定</li>
                                    <li>只有綁定的帳號可以使用此工具</li>
                                    <li>這是為了防止工具濫用的安全機制</li>
                                    <li>如需更換綁定帳號，請聯繫管理員</li>
                                </ul>
                            </div>
                        </div>
                    `;
                } else {
                    // 需要授權碼綁定
                    html += `
                        <div style="margin-top: 15px; padding: 10px; background: rgba(23, 162, 184, 0.1); border-radius: 8px; border: 2px solid #17a2b8;">
                            <strong>🔐 設備綁定：</strong>
                            <p style="margin: 5px 0;">${status.auth_message || '此設備尚未綁定Facebook帳號'}</p>
                            <div style="margin-top: 15px; padding: 10px; background: rgba(255, 255, 255, 0.8); border-radius: 6px;">
                                <strong>📋 綁定流程：</strong>
                                <ol style="margin: 5px 0 0 20px; color: #0c5460;">
                                    <li>管理員提供授權碼給您</li>
                                    <li>確保已登入Facebook (當前用戶: ${status.user_id})</li>
                                    <li>輸入授權碼並點擊綁定</li>
                                    <li>系統自動綁定您的Facebook ID到此設備</li>
                                </ol>
                            </div>
                            <div style="margin-top: 15px;">
                                <input type="text" id="authCodeInput" placeholder="請輸入授權碼" style="padding: 8px; margin-right: 10px; border: 1px solid #17a2b8; border-radius: 4px; width: 200px;">
                                <button onclick="registerDevice()" style="padding: 8px 15px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">綁定設備</button>
                            </div>
                            <div style="margin-top: 10px; font-size: 0.9rem; color: #0c5460;">
                                <strong>🔒 安全提示：</strong>綁定後此設備只能被您的Facebook帳號使用
                            </div>
                        </div>
                    `;
                }
            } else if (status.login_status && status.authorized) {
                // 登入成功且已授權時停止監控
                stopBrowserMonitoring();
                showAlert('✅ Facebook登入並授權成功！可以開始使用工具功能。', 'success');
            }

            html += `</div>`;
            container.innerHTML = html;
        }

        // 註冊設備並綁定Facebook ID
        async function registerDevice() {
            const authCode = document.getElementById('authCodeInput').value.trim();
            if (!authCode) {
                alert('請輸入授權碼');
                return;
            }

            try {
                // 檢查Facebook登入狀態
                const status = await apiCall('/api/browser/check', { method: 'POST' });
                if (!status.user_id) {
                    alert('請先登入Facebook，系統需要抓取您的Facebook ID進行綁定');
                    return;
                }

                // 確認綁定
                const confirmMessage = `即將綁定您的Facebook帳號到此設備：\n\nFacebook ID: ${status.user_id}\n設備ID: ${status.device_id}\n\n綁定後此設備只能被您的Facebook帳號使用。\n\n確定要繼續嗎？`;

                if (!confirm(confirmMessage)) {
                    return;
                }

                // 執行綁定
                const result = await apiCall('/api/auth/register', {
                    method: 'POST',
                    body: JSON.stringify({
                        auth_code: authCode,
                        device_name: `Device-${status.device_id.substring(0, 8)}`
                    })
                });

                if (result.success) {
                    alert(`設備綁定成功！\n\nFacebook ID: ${result.facebook_user_id}\n設備名稱: ${result.device_name}\n有效期至: ${new Date(result.expires_at).toLocaleString()}`);
                    checkBrowserStatus(); // 重新檢查狀態
                } else {
                    alert(`綁定失敗: ${result.message}`);
                }

            } catch (error) {
                alert(`綁定失敗: ${error.message}`);
            }
        }

        // 載入社團列表
        async function loadGroups() {
            try {
                console.log('🔄 正在載入社團列表...');
                const response = await apiCall('/api/groups');

                console.log('📋 API響應:', {
                    user_id: response.user_id,
                    groups_count: response.groups ? response.groups.length : 0,
                    total_groups: response.total_groups,
                    has_error: !!response.error
                });

                if (response.error) {
                    console.error('API返回錯誤:', response.error);
                    showAlert(`載入社團列表失敗: ${response.error}`, 'danger');
                    return;
                }

                groupsData = response.groups || [];
                selectedGroups = response.selected_groups || [];

                console.log(`📊 載入了 ${groupsData.length} 個社團`);

                if (groupsData.length > 0) {
                    elements.userIdDisplay.textContent = response.user_id || '未知';
                    renderGroupsList();
                    elements.groupsList.style.display = 'block';
                    showAlert(`✅ 成功載入 ${groupsData.length} 個社團`, 'success');
                } else {
                    elements.groupsList.style.display = 'none';
                    showAlert('📭 沒有找到社團數據，請先抓取社團列表', 'warning');
                }
            } catch (error) {
                console.error('載入社團列表失敗:', error);
                showAlert(`載入社團列表失敗: ${error.message}`, 'danger');
            }
        }

        // 渲染社團列表
        function renderGroupsList() {
            const container = elements.groupsContainer;
            container.innerHTML = '';

            if (groupsData.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; margin: 20px 0;">暫無社團數據</p>';
                return;
            }

            groupsData.forEach(group => {
                const groupItem = document.createElement('div');
                groupItem.style.cssText = `
                    display: flex;
                    align-items: center;
                    padding: 12px;
                    margin-bottom: 8px;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    background: #fafafa;
                    transition: all 0.2s ease;
                `;

                const isSelected = selectedGroups.includes(group.id);

                groupItem.innerHTML = `
                    <input type="checkbox" id="group_${group.id}" ${isSelected ? 'checked' : ''}
                           style="margin-right: 12px; transform: scale(1.2);">
                    <div style="flex: 1;">
                        <div style="font-weight: 500; color: #2c3e50; margin-bottom: 4px;">
                            ${group.name || `社團 ${group.id}`}
                        </div>
                        <div style="font-size: 0.85rem; color: #666;">
                            ID: ${group.id} | ${group.visitTime || '未知造訪時間'}
                        </div>
                    </div>
                `;

                const checkbox = groupItem.querySelector('input[type="checkbox"]');
                checkbox.addEventListener('change', async function() {
                    if (this.checked) {
                        if (!selectedGroups.includes(group.id)) {
                            selectedGroups.push(group.id);
                        }
                    } else {
                        selectedGroups = selectedGroups.filter(id => id !== group.id);
                    }
                    updateSelectedCount();

                    // 自動確認選擇
                    if (selectedGroups.length > 0) {
                        try {
                            await apiCall('/api/groups/select', {
                                method: 'POST',
                                body: JSON.stringify({ selected_group_ids: selectedGroups })
                            });
                            console.log(`✅ 自動確認選擇 ${selectedGroups.length} 個社團`);
                        } catch (error) {
                            console.error('自動確認選擇失敗:', error);
                        }
                    }
                });

                container.appendChild(groupItem);
            });

            updateSelectedCount();
        }

        // 更新選中數量
        function updateSelectedCount() {
            elements.selectedCount.textContent = selectedGroups.length;
            elements.confirmSelection.disabled = selectedGroups.length === 0;
        }

        // 刪除模式切換
        const deleteModeRadios = document.querySelectorAll('input[name="deleteMode"]');
        deleteModeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'all') {
                    elements.allDeleteInfo.style.display = 'block';
                    elements.tagDeleteInfo.style.display = 'none';
                    elements.startDeleter.textContent = '🗑️ 開始全部刪除';
                } else {
                    elements.allDeleteInfo.style.display = 'none';
                    elements.tagDeleteInfo.style.display = 'block';
                    elements.startDeleter.textContent = '🎯 開始針對性刪除';
                }
            });
        });

        // API 調用函數
        async function apiCall(url, options = {}) {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                const error = await response.text();
                throw new Error(error || `HTTP ${response.status}`);
            }
            
            return response.json();
        }

        // 顯示提醒
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            const firstSection = document.querySelector('.section');
            firstSection.parentNode.insertBefore(alertDiv, firstSection);
            
            setTimeout(() => alertDiv.remove(), 5000);
        }

        // 更新狀態指示器
        function updateStatusIndicator(status) {
            const indicator = elements.statusDisplay.querySelector('.status-indicator');
            indicator.className = `status-indicator status-${status}`;
        }

        // 更新進度條
        function updateProgress(elementId, progress, text) {
            const progressContainer = document.getElementById(`${elementId}Progress`);
            const progressFill = document.getElementById(`${elementId}ProgressFill`);
            const progressText = document.getElementById(`${elementId}ProgressText`);
            
            if (progress > 0) {
                progressContainer.style.display = 'block';
            }
            
            progressFill.style.width = `${progress}%`;
            progressText.textContent = text;
        }

        // 瀏覽器檢查事件監聽器
        elements.checkBrowser.addEventListener('click', checkBrowserStatus);
        elements.stopMonitoring.addEventListener('click', () => {
            stopBrowserMonitoring();
            showAlert('⏹️ 已停止監控瀏覽器狀態', 'info');
        });

        // 社團選擇事件監聽器
        elements.selectAllGroups.addEventListener('click', async () => {
            selectedGroups = groupsData.map(group => group.id);
            renderGroupsList();

            // 自動確認選擇
            if (selectedGroups.length > 0) {
                try {
                    await apiCall('/api/groups/select', {
                        method: 'POST',
                        body: JSON.stringify({ selected_group_ids: selectedGroups })
                    });
                    showAlert(`✅ 已全選並確認 ${selectedGroups.length} 個社團`, 'success');
                } catch (error) {
                    showAlert('自動確認選擇失敗', 'danger');
                }
            }
        });

        elements.deselectAllGroups.addEventListener('click', async () => {
            selectedGroups = [];
            renderGroupsList();

            // 自動確認選擇（清空）
            try {
                await apiCall('/api/groups/select', {
                    method: 'POST',
                    body: JSON.stringify({ selected_group_ids: [] })
                });
                showAlert('✅ 已取消所有選擇', 'info');
            } catch (error) {
                showAlert('自動確認選擇失敗', 'danger');
            }
        });

        elements.confirmSelection.addEventListener('click', async () => {
            try {
                await apiCall('/api/groups/select', {
                    method: 'POST',
                    body: JSON.stringify({ selected_group_ids: selectedGroups })
                });
                showAlert(`已確認選擇 ${selectedGroups.length} 個社團`, 'success');
            } catch (error) {
                showAlert(`確認選擇失敗: ${error.message}`, 'danger');
            }
        });

        // 事件監聽器
        elements.startCrawler.addEventListener('click', async () => {
            try {
                await apiCall('/api/crawler/start', { method: 'POST' });
                showAlert('社團抓取任務已開始', 'info');
            } catch (error) {
                showAlert(`啟動抓取失敗: ${error.message}`, 'danger');
            }
        });

        elements.stopCrawler.addEventListener('click', async () => {
            try {
                await apiCall('/api/crawler/stop', { method: 'POST' });
                showAlert('已請求停止抓取任務', 'warning');
            } catch (error) {
                showAlert(`停止抓取失敗: ${error.message}`, 'danger');
            }
        });

        elements.refreshGroups.addEventListener('click', async () => {
            try {
                showAlert('🔄 正在刷新社團列表...', 'info');
                await loadGroups();
            } catch (error) {
                showAlert(`刷新社團列表失敗: ${error.message}`, 'danger');
            }
        });

        elements.startDeleter.addEventListener('click', async () => {
            try {
                // 檢查是否已選擇社團
                if (selectedGroups.length === 0) {
                    showAlert('請先抓取社團並選擇要處理的社團', 'warning');
                    return;
                }

                const selectedMode = document.querySelector('input[name="deleteMode"]:checked').value;

                if (selectedMode === 'all') {
                    // 全部刪除模式
                    if (!confirm(`⚠️ 確定要開始全部刪除嗎？\n\n將在以下 ${selectedGroups.length} 個社團中刪除您的所有可見貼文：\n${getSelectedGroupNames().join('\n')}\n\n此操作不可逆！`)) {
                        return;
                    }

                    await apiCall('/api/deleter/start', {
                        method: 'POST',
                        body: JSON.stringify({
                            delete_all: true,
                            target_tags: []
                        })
                    });

                    showAlert(`全部刪除任務已開始，將處理 ${selectedGroups.length} 個社團`, 'info');
                } else {
                    // 針對性刪除模式
                    const tagsText = elements.targetTags.value.trim();
                    if (!tagsText) {
                        showAlert('請輸入目標標籤', 'warning');
                        return;
                    }

                    const targetTags = tagsText.split('\n')
                        .map(tag => tag.trim())
                        .filter(tag => tag.length > 0);

                    if (targetTags.length === 0) {
                        showAlert('請輸入有效的目標標籤', 'warning');
                        return;
                    }

                    if (!confirm(`⚠️ 確定要開始針對性刪除嗎？\n\n目標標籤：${targetTags.join(', ')}\n處理社團：${selectedGroups.length} 個\n\n只有同時包含所有這些標籤的貼文才會被刪除。`)) {
                        return;
                    }

                    await apiCall('/api/deleter/start', {
                        method: 'POST',
                        body: JSON.stringify({
                            delete_all: false,
                            target_tags: targetTags
                        })
                    });

                    showAlert(`針對性刪除任務已開始，目標標籤：${targetTags.join(', ')}，將處理 ${selectedGroups.length} 個社團`, 'info');
                }
            } catch (error) {
                showAlert(`啟動刪除失敗: ${error.message}`, 'danger');
            }
        });

        // 獲取選中社團的名稱
        function getSelectedGroupNames() {
            return selectedGroups.map(groupId => {
                const group = groupsData.find(g => g.id === groupId);
                return group ? (group.name || `社團 ${group.id}`) : `社團 ${groupId}`;
            });
        }

        elements.stopDeleter.addEventListener('click', async () => {
            try {
                await apiCall('/api/deleter/stop', { method: 'POST' });
                showAlert('已請求停止刪除任務', 'warning');
            } catch (error) {
                showAlert(`停止刪除失敗: ${error.message}`, 'danger');
            }
        });

        // 狀態輪詢
        async function pollStatus() {
            try {
                console.log('🔄 正在輪詢狀態...');
                const status = await apiCall('/api/status');
                console.log('📊 狀態響應:', status);

                // 更新狀態顯示
                if (status.crawler_status === 'running' || status.deleter_status === 'running') {
                    updateStatusIndicator('running');
                } else if (status.crawler_status === 'completed' || status.deleter_status === 'completed') {
                    updateStatusIndicator('completed');
                } else if (status.crawler_status === 'error' || status.deleter_status === 'error') {
                    updateStatusIndicator('error');
                } else {
                    updateStatusIndicator('idle');
                }

                // 更新狀態文字
                let statusText = '系統待機中...';
                if (status.crawler_status === 'running') {
                    statusText = `🕷️ 抓取中... ${status.crawler_progress || ''}`;
                } else if (status.deleter_status === 'running') {
                    statusText = `🗑️ 刪除中... ${status.deleter_progress || ''}`;
                } else if (status.crawler_status === 'completed') {
                    statusText = '✅ 抓取完成';
                } else if (status.deleter_status === 'completed') {
                    statusText = '✅ 刪除完成';
                } else if (status.crawler_status === 'error') {
                    statusText = '❌ 抓取錯誤';
                } else if (status.deleter_status === 'error') {
                    statusText = '❌ 刪除錯誤';
                }

                elements.statusDisplay.innerHTML = `<p><span class="status-indicator status-${
                    status.crawler_status === 'running' || status.deleter_status === 'running' ? 'running' :
                    status.crawler_status === 'completed' || status.deleter_status === 'completed' ? 'completed' :
                    status.crawler_status === 'error' || status.deleter_status === 'error' ? 'error' : 'idle'
                }"></span>${statusText}</p>`;

                // 更新按鈕狀態
                if (status.crawler_status === 'running') {
                    elements.startCrawler.disabled = true;
                    elements.stopCrawler.disabled = false;
                } else {
                    elements.startCrawler.disabled = false;
                    elements.stopCrawler.disabled = true;
                }

                if (status.deleter_status === 'running') {
                    elements.startDeleter.disabled = true;
                    elements.stopDeleter.disabled = false;
                } else {
                    elements.startDeleter.disabled = false;
                    elements.stopDeleter.disabled = true;
                }

                // 更新進度條
                if (status.crawler_status === 'running' && status.crawler_progress) {
                    updateProgress('crawler', status.crawler_progress.percentage || 0, status.crawler_progress.text || '處理中...');
                } else if (status.crawler_status !== 'running') {
                    elements.crawlerProgress.style.display = 'none';
                }

                // 檢查抓取是否完成，如果完成則載入社團列表
                if (status.crawler_status === 'completed' && status.crawler_result && status.crawler_result.success) {
                    loadGroups();
                }

                if (status.deleter_status === 'running' && status.deleter_progress) {
                    updateProgress('deleter', status.deleter_progress.percentage || 0, status.deleter_progress.text || '處理中...');
                } else if (status.deleter_status !== 'running') {
                    elements.deleterProgress.style.display = 'none';
                }

            } catch (error) {
                console.error('狀態輪詢失敗:', error);
            }
        }

        // 開始輪詢
        setInterval(pollStatus, 2000);

        // 頁面卸載時清理監控
        window.addEventListener('beforeunload', () => {
            stopBrowserMonitoring();
        });

        // 初始化
        pollStatus();
        loadGroups(); // 頁面載入時嘗試載入已有的社團列表

        // 自動開始瀏覽器檢查
        setTimeout(() => {
            console.log('🔄 自動開始瀏覽器檢查...');
            checkBrowserStatus(true); // 頁面載入後立即自動檢查

            // 自動開始監控，持續檢查直到登入並授權
            startBrowserMonitoring();
        }, 500); // 500ms後開始，更快響應
    </script>
</body>
</html>