<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Tool - 授權管理</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft JhengHei', Arial, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #3498db; }
        .stat-label { color: #666; margin-top: 5px; }
        .section { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .section h2 { color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; margin: 5px; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn:hover { opacity: 0.8; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background: #f8f9fa; font-weight: bold; }
        .table tr:hover { background: #f8f9fa; }
        .status-active { color: #27ae60; font-weight: bold; }
        .status-inactive { color: #e74c3c; font-weight: bold; }
        .alert { padding: 15px; border-radius: 4px; margin: 10px 0; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal-content { background: white; margin: 5% auto; padding: 20px; border-radius: 8px; width: 90%; max-width: 500px; }
        .close { float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
        .close:hover { color: #e74c3c; }
        .code-display { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 18px; text-align: center; border: 2px solid #3498db; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Facebook Tool - 授權管理系統</h1>
            <p>管理授權碼、設備綁定和使用監控</p>
        </div>

        <!-- 統計數據 -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="totalDevices">-</div>
                <div class="stat-label">總設備數</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeDevices">-</div>
                <div class="stat-label">活躍設備</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalAuthCodes">-</div>
                <div class="stat-label">總授權碼</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="usedAuthCodes">-</div>
                <div class="stat-label">已使用授權碼</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="recentActivities">-</div>
                <div class="stat-label">近7天活動</div>
            </div>
        </div>

        <!-- 授權碼管理 -->
        <div class="section">
            <h2>🎫 授權碼管理</h2>
            
            <div style="margin-bottom: 20px;">
                <button class="btn btn-success" onclick="showCreateCodeModal()">🆕 生成新授權碼</button>
                <button class="btn btn-primary" onclick="loadAuthCodes()">🔄 刷新列表</button>
                <button class="btn btn-warning" onclick="cleanDatabase('codes')">🧹 清理授權碼</button>
            </div>
            
            <div id="authCodesContainer">
                <p>點擊「刷新列表」載入授權碼...</p>
            </div>
        </div>

        <!-- 設備管理 -->
        <div class="section">
            <h2>📱 設備管理</h2>
            
            <div style="margin-bottom: 20px;">
                <button class="btn btn-primary" onclick="loadDevices()">🔄 刷新列表</button>
                <button class="btn btn-warning" onclick="cleanDatabase('devices')">🧹 清理設備記錄</button>
            </div>
            
            <div id="devicesContainer">
                <p>點擊「刷新列表」載入設備...</p>
            </div>
        </div>

        <!-- 使用日誌 -->
        <div class="section">
            <h2>📝 使用日誌</h2>
            
            <div style="margin-bottom: 20px;">
                <button class="btn btn-primary" onclick="loadUsageLogs()">🔄 刷新日誌</button>
                <button class="btn btn-warning" onclick="cleanDatabase('logs')">🧹 清理日誌</button>
                <button class="btn btn-danger" onclick="cleanDatabase('all')">🗑️ 清理所有數據</button>
            </div>
            
            <div id="logsContainer">
                <p>點擊「刷新日誌」載入使用記錄...</p>
            </div>
        </div>
    </div>

    <!-- 創建授權碼模態框 -->
    <div id="createCodeModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeCreateCodeModal()">&times;</span>
            <h2>🆕 生成新授權碼</h2>
            
            <div class="form-group">
                <label>描述 (可選):</label>
                <input type="text" id="codeDescription" placeholder="例如：用戶A的授權碼">
            </div>
            
            <div class="form-group">
                <label>設備槽位數量:</label>
                <input type="number" id="deviceSlots" value="1" min="1" max="10">
            </div>
            
            <div class="form-group">
                <label>有效天數:</label>
                <input type="number" id="daysValid" value="30" min="1" max="365">
            </div>
            
            <button class="btn btn-success" onclick="createAuthCode()">🎫 生成授權碼</button>
            <button class="btn" onclick="closeCreateCodeModal()">取消</button>
        </div>
    </div>

    <!-- 授權碼顯示模態框 -->
    <div id="codeResultModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeCodeResultModal()">&times;</span>
            <h2>🎉 授權碼生成成功</h2>
            
            <div class="code-display" id="generatedCode">
                授權碼將顯示在這裡
            </div>
            
            <div id="codeDetails"></div>
            
            <button class="btn btn-primary" onclick="copyCode()">📋 複製授權碼</button>
            <button class="btn" onclick="closeCodeResultModal()">關閉</button>
        </div>
    </div>

    <script>
        // API調用函數
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                return await response.json();
            } catch (error) {
                console.error('API調用失敗:', error);
                throw error;
            }
        }

        // 載入統計數據
        async function loadStats() {
            try {
                const result = await apiCall('/api/admin/stats');
                if (result.success) {
                    const stats = result.stats;
                    document.getElementById('totalDevices').textContent = stats.total_devices;
                    document.getElementById('activeDevices').textContent = stats.active_devices;
                    document.getElementById('totalAuthCodes').textContent = stats.total_auth_codes;
                    document.getElementById('usedAuthCodes').textContent = stats.used_auth_codes;
                    document.getElementById('recentActivities').textContent = stats.recent_activities;
                }
            } catch (error) {
                console.error('載入統計失敗:', error);
            }
        }

        // 載入授權碼列表
        async function loadAuthCodes() {
            try {
                const result = await apiCall('/api/admin/auth-codes');
                if (result.success) {
                    displayAuthCodes(result.auth_codes);
                }
            } catch (error) {
                console.error('載入授權碼失敗:', error);
            }
        }

        // 顯示授權碼列表
        function displayAuthCodes(codes) {
            const container = document.getElementById('authCodesContainer');
            
            if (codes.length === 0) {
                container.innerHTML = '<p>暫無授權碼記錄</p>';
                return;
            }
            
            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>授權碼</th>
                            <th>使用狀況</th>
                            <th>狀態</th>
                            <th>有效期</th>
                            <th>創建時間</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            codes.forEach(code => {
                const status = code.is_active ? '<span class="status-active">有效</span>' : '<span class="status-inactive">無效</span>';
                const expires = code.expires_at ? new Date(code.expires_at).toLocaleString() : '永久';
                const created = new Date(code.created_at).toLocaleString();
                
                html += `
                    <tr>
                        <td><code>${code.code}</code></td>
                        <td>${code.used_slots}/${code.device_slots}</td>
                        <td>${status}</td>
                        <td>${expires}</td>
                        <td>${created}</td>
                        <td>
                            ${code.is_active ? `<button class="btn btn-danger" onclick="deactivateAuthCode(${code.id})">停用</button>` : ''}
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 載入設備列表
        async function loadDevices() {
            try {
                const result = await apiCall('/api/admin/devices');
                if (result.success) {
                    displayDevices(result.devices);
                }
            } catch (error) {
                console.error('載入設備失敗:', error);
            }
        }

        // 顯示設備列表
        function displayDevices(devices) {
            const container = document.getElementById('devicesContainer');
            
            if (devices.length === 0) {
                container.innerHTML = '<p>暫無設備記錄</p>';
                return;
            }
            
            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>設備名稱</th>
                            <th>設備ID</th>
                            <th>Facebook ID</th>
                            <th>狀態</th>
                            <th>權限</th>
                            <th>綁定時間</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            devices.forEach(device => {
                const status = device.is_active ? '<span class="status-active">有效</span>' : '<span class="status-inactive">無效</span>';
                const authorized = new Date(device.authorized_at).toLocaleString();
                const permissions = device.permissions ? device.permissions.join(', ') : '無';
                
                html += `
                    <tr>
                        <td>${device.device_name}</td>
                        <td><code>${device.device_id}</code></td>
                        <td><code>${device.user_id}</code></td>
                        <td>${status}</td>
                        <td>${permissions}</td>
                        <td>${authorized}</td>
                        <td>
                            ${device.is_active ? `<button class="btn btn-danger" onclick="revokeDevice('${device.device_id}')">撤銷</button>` : ''}
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 載入使用日誌
        async function loadUsageLogs() {
            try {
                const result = await apiCall('/api/admin/usage-logs');
                if (result.success) {
                    displayUsageLogs(result.logs);
                }
            } catch (error) {
                console.error('載入日誌失敗:', error);
            }
        }

        // 顯示使用日誌
        function displayUsageLogs(logs) {
            const container = document.getElementById('logsContainer');
            
            if (logs.length === 0) {
                container.innerHTML = '<p>暫無使用記錄</p>';
                return;
            }
            
            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>時間</th>
                            <th>Facebook ID</th>
                            <th>設備ID</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            logs.forEach(log => {
                const timestamp = new Date(log.timestamp).toLocaleString();
                
                html += `
                    <tr>
                        <td>${timestamp}</td>
                        <td><code>${log.user_id}</code></td>
                        <td><code>${log.device_id}</code></td>
                        <td>${log.action}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 顯示創建授權碼模態框
        function showCreateCodeModal() {
            document.getElementById('createCodeModal').style.display = 'block';
        }

        // 關閉創建授權碼模態框
        function closeCreateCodeModal() {
            document.getElementById('createCodeModal').style.display = 'none';
        }

        // 關閉授權碼結果模態框
        function closeCodeResultModal() {
            document.getElementById('codeResultModal').style.display = 'none';
        }

        // 創建授權碼
        async function createAuthCode() {
            const description = document.getElementById('codeDescription').value;
            const deviceSlots = parseInt(document.getElementById('deviceSlots').value);
            const daysValid = parseInt(document.getElementById('daysValid').value);
            
            try {
                const result = await apiCall('/api/admin/create-auth-code', {
                    method: 'POST',
                    body: JSON.stringify({
                        description: description,
                        device_slots: deviceSlots,
                        days_valid: daysValid
                    })
                });
                
                if (result.success) {
                    closeCreateCodeModal();
                    showCodeResult(result.auth_code);
                    loadStats();
                    loadAuthCodes();
                } else {
                    alert(`創建失敗: ${result.error}`);
                }
                
            } catch (error) {
                alert(`創建失敗: ${error.message}`);
            }
        }

        // 顯示生成的授權碼
        function showCodeResult(authCode) {
            document.getElementById('generatedCode').textContent = authCode.code;
            document.getElementById('codeDetails').innerHTML = `
                <p><strong>設備槽位:</strong> ${authCode.device_slots}</p>
                <p><strong>有效期至:</strong> ${new Date(authCode.expires_at).toLocaleString()}</p>
                <p><strong>描述:</strong> ${authCode.description || '無'}</p>
            `;
            document.getElementById('codeResultModal').style.display = 'block';
        }

        // 複製授權碼
        function copyCode() {
            const code = document.getElementById('generatedCode').textContent;
            navigator.clipboard.writeText(code).then(() => {
                alert('授權碼已複製到剪貼板！');
            });
        }

        // 撤銷設備授權
        async function revokeDevice(deviceId) {
            if (!confirm(`確定要撤銷設備 ${deviceId} 的授權嗎？`)) {
                return;
            }
            
            try {
                const result = await apiCall('/api/admin/revoke-device', {
                    method: 'POST',
                    body: JSON.stringify({ device_id: deviceId })
                });
                
                if (result.success) {
                    alert(result.message);
                    loadDevices();
                    loadStats();
                } else {
                    alert(`撤銷失敗: ${result.error}`);
                }
                
            } catch (error) {
                alert(`撤銷失敗: ${error.message}`);
            }
        }

        // 停用授權碼
        async function deactivateAuthCode(codeId) {
            if (!confirm('確定要停用此授權碼嗎？')) {
                return;
            }
            
            try {
                const result = await apiCall('/api/admin/deactivate-auth-code', {
                    method: 'POST',
                    body: JSON.stringify({ code_id: codeId })
                });
                
                if (result.success) {
                    alert(result.message);
                    loadAuthCodes();
                    loadStats();
                } else {
                    alert(`停用失敗: ${result.error}`);
                }
                
            } catch (error) {
                alert(`停用失敗: ${error.message}`);
            }
        }

        // 清理資料庫
        async function cleanDatabase(type) {
            const typeNames = {
                'devices': '設備記錄',
                'codes': '授權碼記錄', 
                'logs': '使用日誌',
                'all': '所有數據'
            };
            
            if (!confirm(`確定要清理${typeNames[type]}嗎？此操作不可恢復！`)) {
                return;
            }
            
            try {
                const result = await apiCall('/api/admin/clean-database', {
                    method: 'POST',
                    body: JSON.stringify({ type: type })
                });
                
                if (result.success) {
                    alert(result.message);
                    loadStats();
                    loadAuthCodes();
                    loadDevices();
                    loadUsageLogs();
                } else {
                    alert(`清理失敗: ${result.error}`);
                }
                
            } catch (error) {
                alert(`清理失敗: ${error.message}`);
            }
        }

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            
            // 每30秒自動刷新統計數據
            setInterval(loadStats, 30000);
        });
    </script>
</body>
</html>
