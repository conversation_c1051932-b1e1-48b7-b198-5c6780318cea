@echo off
echo Starting Facebook Tool - Auth Admin Web Interface
echo ===================================================

echo Installing dependencies...
pip install Flask Flask-CORS supabase python-dotenv

echo Starting admin web interface...
echo.
echo Opening admin panel in 3 seconds...
echo URL: http://localhost:5001
echo.

REM Start admin server and open browser
start /b python auth_admin.py
timeout /t 3 /nobreak >nul
start http://localhost:5001

echo Admin panel is running on port 5001
echo Main app can run on port 5000 simultaneously
echo.
echo Press any key to stop admin panel...
pause >nul

REM Kill Python processes
taskkill /f /im python.exe >nul 2>&1
