@echo off
chcp 65001 >nul
echo Building Facebook Tool Executable...
echo.

REM Clean user data
echo Cleaning user data...
call clean_simple.bat

REM Install PyInstaller if not installed
echo Installing PyInstaller...
pip install pyinstaller

REM Create executable using spec file
echo Creating executable...
pyinstaller facebook_tool.spec

echo.
echo Build complete!
echo Executable is in: dist\FacebookTool.exe
echo.

REM Create a simple launcher
echo Creating launcher...
echo @echo off > dist\start_facebook_tool.bat
echo echo Starting Facebook Tool... >> dist\start_facebook_tool.bat
echo start /b FacebookTool.exe >> dist\start_facebook_tool.bat
echo timeout /t 3 /nobreak ^>nul >> dist\start_facebook_tool.bat
echo start http://localhost:5000 >> dist\start_facebook_tool.bat
echo echo Tool is running. Close this window to stop. >> dist\start_facebook_tool.bat
echo pause >> dist\start_facebook_tool.bat

echo.
echo Launcher created: dist\start_facebook_tool.bat
echo.
echo To distribute:
echo 1. Copy the entire 'dist' folder to target computer
echo 2. Run 'start_facebook_tool.bat' on target computer
echo.
pause
