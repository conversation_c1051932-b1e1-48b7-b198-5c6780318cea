@echo off
echo Final Safe Build Process...
echo.

echo Step 1: Complete environment cleanup
REM Kill any running Python processes that might lock files
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Delete all user data files from all possible locations
echo Cleaning user data files...
for %%f in (urls.txt crawl_info.json facebook_deleter.log) do (
    if exist "%%f" del /f /q "%%f" >nul 2>&1
    if exist "%TEMP%\%%f" del /f /q "%TEMP%\%%f" >nul 2>&1
    if exist "%USERPROFILE%\AppData\Local\Temp\%%f" del /f /q "%USERPROFILE%\AppData\Local\Temp\%%f" >nul 2>&1
)

REM Clean all cache and build directories
echo Cleaning cache directories...
for %%d in (__pycache__ build dist .pytest_cache) do (
    if exist "%%d" rmdir /s /q "%%d" >nul 2>&1
)

REM Clean PyInstaller cache
if exist "%USERPROFILE%\.pyinstaller" rmdir /s /q "%USERPROFILE%\.pyinstaller" >nul 2>&1
if exist "%APPDATA%\pyinstaller" rmdir /s /q "%APPDATA%\pyinstaller" >nul 2>&1

echo Step 2: Install/Update PyInstaller
pip install --upgrade pyinstaller

echo Step 3: Build with maximum security
pyinstaller --clean --noconfirm --onefile ^
    --exclude-module tkinter ^
    --exclude-module matplotlib ^
    --exclude-module numpy ^
    --exclude-module pandas ^
    --add-data "templates;templates" ^
    --name FacebookTool_Safe ^
    app.py

echo Step 4: Verify build
if exist "dist\FacebookTool_Safe.exe" (
    echo Build successful!
    
    echo Step 5: Check for user data
    python check_exe_content.py "dist\FacebookTool_Safe.exe"
    
    echo Step 6: Create launcher
    echo @echo off > dist\start_safe_tool.bat
    echo echo Starting Safe Facebook Tool... >> dist\start_safe_tool.bat
    echo start /b FacebookTool_Safe.exe >> dist\start_safe_tool.bat
    echo timeout /t 3 /nobreak ^^^>nul >> dist\start_safe_tool.bat
    echo start http://localhost:5000 >> dist\start_safe_tool.bat
    echo pause >> dist\start_safe_tool.bat
    
    echo.
    echo ✅ Safe build completed!
    echo Executable: dist\FacebookTool_Safe.exe
    echo Launcher: dist\start_safe_tool.bat
    echo.
    echo Please run the content check to verify safety.
) else (
    echo ❌ Build failed!
)

pause
