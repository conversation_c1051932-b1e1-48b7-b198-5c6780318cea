@echo off
echo Ultra Clean Build Process...
echo.

echo Step 1: Complete cleanup
REM Delete all possible user data files
for %%f in (urls.txt crawl_info.json facebook_deleter.log) do (
    if exist "%%f" (
        echo Deleting %%f
        del "%%f"
    )
)

REM Clean temp directories
for %%d in ("%TEMP%" "%USERPROFILE%\AppData\Local\Temp") do (
    for %%f in (urls.txt crawl_info.json facebook_deleter.log) do (
        if exist "%%d\%%f" (
            echo Deleting %%d\%%f
            del "%%d\%%f"
        )
    )
)

REM Clean Python cache and build directories
for %%d in (__pycache__ build dist) do (
    if exist "%%d" (
        echo Removing %%d
        rmdir /s /q "%%d"
    )
)

REM Clean PyInstaller cache
if exist "%USERPROFILE%\.pyinstaller" (
    echo Cleaning PyInstaller cache
    rmdir /s /q "%USERPROFILE%\.pyinstaller"
)

echo Step 2: Create clean working copy
mkdir temp_build
copy *.py temp_build\
copy *.spec temp_build\
copy requirements.txt temp_build\
xcopy templates temp_build\templates\ /e /i
if exist static xcopy static temp_build\static\ /e /i

cd temp_build

echo Step 3: Install PyInstaller
pip install pyinstaller

echo Step 4: Build with maximum cleaning
pyinstaller --clean --noconfirm --onefile facebook_tool.spec

echo Step 5: Move result back
move dist\FacebookTool.exe ..\FacebookTool_clean.exe

cd ..
rmdir /s /q temp_build

echo Step 6: Create launcher
echo @echo off > start_clean_tool.bat
echo echo Starting Clean Facebook Tool... >> start_clean_tool.bat
echo start /b FacebookTool_clean.exe >> start_clean_tool.bat
echo timeout /t 3 /nobreak ^>nul >> start_clean_tool.bat
echo start http://localhost:5000 >> start_clean_tool.bat
echo pause >> start_clean_tool.bat

echo.
echo Ultra clean build completed!
echo Clean executable: FacebookTool_clean.exe
echo Launcher: start_clean_tool.bat
echo.
pause
