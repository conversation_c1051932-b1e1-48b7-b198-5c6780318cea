# Facebook 工具後端網頁版

這是Facebook社團抓取和貼文刪除工具的Web版本，提供了友好的網頁界面來管理您的Facebook操作。

## 功能特點

### 🔄 社團抓取功能
- 自動抓取您已加入的所有Facebook社團
- 生成社團個人動態頁面URL列表
- 支持數字ID和英文ID的社團
- 實時進度顯示

### 🗑️ 貼文刪除功能
- **針對性刪除**：根據指定標籤刪除特定貼文
- **全部刪除**：刪除所有可見的個人貼文（新功能）
- 支持多標籤組合匹配
- 智能滾動載入和刪除邏輯

### 🌐 Web界面特點
- 現代化響應式設計
- 實時任務進度顯示
- 文件下載功能
- 任務狀態監控
- 操作日誌記錄

## 安裝和使用

### 1. 環境準備
```bash
# 確保已安裝Python 3.7+
python --version

# 安裝依賴（如果需要）
pip install -r requirements.txt
```

### 2. 啟動服務器
#### Windows用戶：
```bash
# 直接雙擊執行
start_server.bat

# 或者在命令行中執行
./start_server.bat
```

#### Linux/Mac用戶：
```bash
# 給腳本執行權限
chmod +x start_server.sh

# 執行腳本
./start_server.sh
```

#### 手動啟動：
```bash
python app.py
```

### 3. 訪問網頁界面
打開瀏覽器訪問：`http://localhost:5000`

## 使用步驟

### 準備工作
1. **啟動Chrome瀏覽器**並登入Facebook
2. **確保Facebook已完全登入**且能正常訪問

### 抓取社團
1. 在網頁界面中點擊「開始抓取社團」
2. 等待抓取進度完成
3. 檢查生成的`urls.txt`文件

### 刪除貼文
1. **針對性刪除**：
   - 輸入目標標籤（用逗號分隔）
   - 點擊「開始刪除」
   
2. **全部刪除**：
   - 勾選「全部刪除模式」
   - 點擊「開始刪除」

## API接口文檔

### 主要API端點

| 方法 | 端點 | 描述 |
|------|------|------|
| GET | `/api/status` | 獲取所有任務狀態 |
| POST | `/api/crawler/start` | 開始社團抓取 |
| POST | `/api/crawler/stop` | 停止社團抓取 |
| POST | `/api/deleter/start` | 開始貼文刪除 |
| POST | `/api/deleter/stop` | 停止貼文刪除 |
| GET | `/api/files/status` | 獲取文件狀態 |
| GET | `/api/urls/download` | 下載urls.txt |
| GET | `/api/crawl_info/download` | 下載抓取信息 |
| GET | `/api/logs/download` | 下載日誌文件 |

### 刪除請求示例
```javascript
// 針對性刪除
{
  "delete_all": false,
  "target_tags": ["標籤1", "標籤2"]
}

// 全部刪除
{
  "delete_all": true,
  "target_tags": []
}
```

## 文件說明

### 核心文件
- `app.py` - Flask後端應用程序
- `facebook_deleter_with_urls.py` - 增強的刪除器類（含全部刪除功能）
- `facebook_groups_crawler.py` - 社團抓取器類
- `templates/index.html` - 網頁界面

### 生成文件
- `urls.txt` - 社團URL列表
- `crawl_info.json` - 詳細抓取信息
- `facebook_deleter.log` - 操作日誌

### 配置文件
- `requirements.txt` - Python依賴
- `start_server.bat` - Windows啟動腳本
- `start_server.sh` - Linux/Mac啟動腳本

## 安全注意事項

⚠️ **重要提醒**：
1. 請確保瀏覽器已正確登入Facebook
2. 建議在使用前備份重要資料
3. 刪除操作不可逆，請謹慎使用
4. 避免過於頻繁的操作以防觸發Facebook限制

## 故障排除

### 常見問題

1. **瀏覽器連接失敗**
   - 確保Chrome瀏覽器已啟動
   - 檢查是否已登入Facebook

2. **抓取失敗**
   - 檢查Facebook登入狀態
   - 確認網絡連接正常

3. **刪除操作無響應**
   - 檢查目標標籤是否正確
   - 確認社團URL是否有效

4. **Python依賴錯誤**
   ```bash
   pip install --upgrade -r requirements.txt
   ```

### 日誌查看
- 網頁界面：查看實時狀態更新
- 日誌文件：`facebook_deleter.log`
- 瀏覽器控制台：檢查前端錯誤

## 更新記錄

### v2.1.0 (修復刪除問題)
- 🔧 修復刪除功能問題：改用分步驟同步方式
- 🔧 替換原有的異步setTimeout邏輯為同步執行
- 🔧 增強JavaScript調試信息和錯誤檢測
- 🔧 改善按鈕和選單項目的搜尋邏輯
- 🔧 增加刪除操作的驗證步驟

### v2.0.0 (Web版本)
- ✅ 新增Web界面管理
- ✅ 新增全部刪除功能
- ✅ 新增實時進度顯示
- ✅ 新增文件下載功能
- ✅ 優化用戶體驗

### v1.0.0 (命令行版本)
- ✅ 基礎社團抓取功能
- ✅ 針對性貼文刪除功能

## 技術支持

如果遇到問題，請檢查：
1. Python版本是否為3.7+
2. 所有依賴是否正確安裝
3. Chrome瀏覽器是否正常運行
4. Facebook是否已正確登入

---

**注意**：此工具僅供個人使用，請遵守Facebook服務條款和相關法律法規。