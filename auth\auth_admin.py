#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授權管理Web界面
提供授權碼生成、設備管理、使用監控的Web界面
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_cors import CORS
import secrets
import string
from datetime import datetime, timedelta
from auth_system import auth_system

app = Flask(__name__, template_folder='templates')
CORS(app)

def generate_auth_code():
    """生成授權碼"""
    return ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(12))

@app.route('/')
def admin_dashboard():
    """管理員儀表板"""
    return render_template('admin_dashboard.html')

@app.route('/api/admin/stats')
def get_stats():
    """獲取統計數據"""
    try:
        # 獲取統計數據
        devices = auth_system.supabase.table('device_authorizations').select('*').execute()
        codes = auth_system.supabase.table('auth_codes').select('*').execute()
        logs = auth_system.supabase.table('usage_logs').select('*').gte(
            'timestamp', (datetime.now() - timedelta(days=7)).isoformat()
        ).execute()
        
        # 活躍設備統計
        active_devices = [d for d in devices.data if d['is_active']]
        
        # 授權碼統計
        active_codes = [c for c in codes.data if c['is_active']]
        used_codes = [c for c in codes.data if c['used_slots'] > 0]
        
        return jsonify({
            "success": True,
            "stats": {
                "total_devices": len(devices.data),
                "active_devices": len(active_devices),
                "total_auth_codes": len(codes.data),
                "active_auth_codes": len(active_codes),
                "used_auth_codes": len(used_codes),
                "recent_activities": len(logs.data)
            }
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/admin/devices')
def get_devices():
    """獲取所有設備列表"""
    try:
        result = auth_system.supabase.table('device_authorizations').select('*').order('created_at', desc=True).execute()
        
        devices = []
        for device in result.data:
            devices.append({
                "id": device['id'],
                "device_id": device['device_id'],
                "device_name": device['device_name'],
                "user_id": device['user_id'],
                "is_active": device['is_active'],
                "permissions": device.get('permissions', []),
                "authorized_at": device['authorized_at'],
                "expires_at": device.get('expires_at'),
                "created_at": device['created_at']
            })
        
        return jsonify({"success": True, "devices": devices})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/admin/auth-codes')
def get_auth_codes():
    """獲取所有授權碼列表"""
    try:
        result = auth_system.supabase.table('auth_codes').select('*').order('created_at', desc=True).execute()
        
        codes = []
        for code in result.data:
            codes.append({
                "id": code['id'],
                "code": code['code'],
                "user_id": code['user_id'],
                "device_slots": code['device_slots'],
                "used_slots": code['used_slots'],
                "is_active": code['is_active'],
                "expires_at": code.get('expires_at'),
                "created_at": code['created_at']
            })
        
        return jsonify({"success": True, "auth_codes": codes})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/admin/create-auth-code', methods=['POST'])
def create_auth_code():
    """創建新授權碼"""
    try:
        data = request.get_json()
        device_slots = data.get('device_slots', 1)
        days_valid = data.get('days_valid', 30)
        description = data.get('description', '')
        
        code = generate_auth_code()
        expires_at = datetime.now() + timedelta(days=days_valid)
        
        result = auth_system.supabase.table('auth_codes').insert({
            'code': code,
            'user_id': '',
            'device_slots': device_slots,
            'expires_at': expires_at.isoformat(),
            'is_active': True
        }).execute()
        
        return jsonify({
            "success": True,
            "message": "授權碼創建成功",
            "auth_code": {
                "code": code,
                "device_slots": device_slots,
                "expires_at": expires_at.isoformat(),
                "description": description
            }
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/admin/revoke-device', methods=['POST'])
def revoke_device():
    """撤銷設備授權"""
    try:
        data = request.get_json()
        device_id = data.get('device_id')
        
        if not device_id:
            return jsonify({"success": False, "error": "缺少設備ID"}), 400
        
        result = auth_system.supabase.table('device_authorizations').update({
            'is_active': False
        }).eq('device_id', device_id).execute()
        
        return jsonify({
            "success": True,
            "message": f"設備 {device_id} 授權已撤銷"
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/admin/deactivate-auth-code', methods=['POST'])
def deactivate_auth_code():
    """停用授權碼"""
    try:
        data = request.get_json()
        code_id = data.get('code_id')
        
        if not code_id:
            return jsonify({"success": False, "error": "缺少授權碼ID"}), 400
        
        result = auth_system.supabase.table('auth_codes').update({
            'is_active': False
        }).eq('id', code_id).execute()
        
        return jsonify({
            "success": True,
            "message": "授權碼已停用"
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/admin/usage-logs')
def get_usage_logs():
    """獲取使用日誌"""
    try:
        days = request.args.get('days', 7, type=int)
        since = datetime.now() - timedelta(days=days)
        
        result = auth_system.supabase.table('usage_logs').select('*').gte(
            'timestamp', since.isoformat()
        ).order('timestamp', desc=True).execute()
        
        logs = []
        for log in result.data:
            logs.append({
                "id": log['id'],
                "user_id": log['user_id'],
                "device_id": log['device_id'],
                "action": log['action'],
                "timestamp": log['timestamp']
            })
        
        return jsonify({"success": True, "logs": logs})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/admin/clean-database', methods=['POST'])
def clean_database():
    """清理資料庫"""
    try:
        data = request.get_json()
        clean_type = data.get('type', 'all')
        
        if clean_type == 'devices':
            result = auth_system.supabase.table('device_authorizations').delete().neq('id', 0).execute()
            message = f"已清理 {len(result.data)} 條設備記錄"
            
        elif clean_type == 'logs':
            result = auth_system.supabase.table('usage_logs').delete().neq('id', 0).execute()
            message = f"已清理 {len(result.data)} 條日誌記錄"
            
        elif clean_type == 'codes':
            result = auth_system.supabase.table('auth_codes').delete().neq('id', 0).execute()
            message = f"已清理 {len(result.data)} 條授權碼記錄"
            
        elif clean_type == 'all':
            logs_result = auth_system.supabase.table('usage_logs').delete().neq('id', 0).execute()
            devices_result = auth_system.supabase.table('device_authorizations').delete().neq('id', 0).execute()
            codes_result = auth_system.supabase.table('auth_codes').delete().neq('id', 0).execute()
            
            message = f"已清理所有數據: 設備 {len(devices_result.data)} 條, 日誌 {len(logs_result.data)} 條, 授權碼 {len(codes_result.data)} 條"
        
        return jsonify({
            "success": True,
            "message": message
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

if __name__ == '__main__':
    print("🌐 授權管理Web界面")
    print("=" * 50)
    print("🌐 請在瀏覽器中訪問: http://localhost:5001")
    print("📋 功能:")
    print("   - 查看統計數據")
    print("   - 生成授權碼")
    print("   - 管理設備授權")
    print("   - 監控使用日誌")
    print("   - 清理測試數據")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=5001)
