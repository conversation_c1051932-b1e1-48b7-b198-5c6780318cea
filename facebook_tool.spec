# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

def filter_data_files(src, names):
    """過濾函數，排除用戶數據文件"""
    excluded = []
    for name in names:
        if name in ['urls.txt', 'crawl_info.json', 'facebook_deleter.log'] or \
           name.endswith('.log') or name.endswith('.pyc') or \
           name == '__pycache__':
            excluded.append(name)
    return excluded

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
    ],
    hiddenimports=[
        'flask',
        'flask_cors',
        'DrissionPage',
        'threading',
        'json',
        'logging',
        'time',
        'os',
        're',
        'typing',
        'subprocess',
        'sys',
        'webbrowser'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FacebookTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加圖標文件路徑，例如 'icon.ico'
)
