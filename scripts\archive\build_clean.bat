@echo off
echo Building Clean Facebook Tool...

echo Step 1: Thorough cleaning
call clean_thorough.bat

echo Step 2: Installing PyInstaller
pip install pyinstaller

echo Step 3: Building with clean spec
pyinstaller --clean --noconfirm facebook_tool.spec

echo Step 4: Verifying no user data in build
echo Checking for user data files...
if exist "dist\FacebookTool\_internal\urls.txt" (
    echo WARNING: urls.txt found in build!
    del "dist\FacebookTool\_internal\urls.txt"
)
if exist "dist\FacebookTool\_internal\crawl_info.json" (
    echo WARNING: crawl_info.json found in build!
    del "dist\FacebookTool\_internal\crawl_info.json"
)
if exist "dist\FacebookTool\_internal\facebook_deleter.log" (
    echo WARNING: facebook_deleter.log found in build!
    del "dist\FacebookTool\_internal\facebook_deleter.log"
)

echo Step 5: Creating launcher
echo @echo off > dist\start_facebook_tool.bat
echo echo Starting Facebook Tool... >> dist\start_facebook_tool.bat
echo start /b FacebookTool.exe >> dist\start_facebook_tool.bat
echo timeout /t 3 /nobreak ^>nul >> dist\start_facebook_tool.bat
echo start http://localhost:5000 >> dist\start_facebook_tool.bat
echo echo Tool is running. Close this window to stop. >> dist\start_facebook_tool.bat
echo pause >> dist\start_facebook_tool.bat

echo.
echo Clean build completed!
echo Files are now safe for distribution.
echo.
pause
