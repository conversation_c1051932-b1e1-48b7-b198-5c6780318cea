# 🔒 開發環境安全指南

## ⚠️ 重要提醒

**絕對不要在包含個人測試數據的環境中進行生產打包！**

## 🔄 開發流程

### 1. **日常開發**
```bash
# 正常開發和測試
python app.py
```

### 2. **測試完成後清理**
```bash
# 清理開發環境
dev_clean.bat
```

### 3. **生產打包**
```bash
# 生產環境打包（自動清理）
build_production.bat
```

## 🛡️ 安全檢查清單

### 開發階段
- [ ] 使用測試用的Facebook帳號
- [ ] 定期清理開發環境
- [ ] 不要提交個人數據到版本控制

### 打包前
- [ ] 運行 `dev_clean.bat`
- [ ] 重啟應用清理內存
- [ ] 確認沒有個人數據文件
- [ ] 使用 `build_production.bat`

### 打包後
- [ ] 運行安全檢查工具
- [ ] 測試可執行文件
- [ ] 確認沒有個人信息洩露

## 📁 文件分類

### 源代碼文件（可打包）
- `*.py` - Python源代碼
- `templates/` - HTML模板
- `static/` - 靜態資源
- `requirements.txt` - 依賴列表

### 用戶數據文件（絕不可打包）
- `urls.txt` - 包含用戶ID的URL列表
- `crawl_info.json` - 抓取信息和用戶ID
- `facebook_deleter.log` - 操作日誌
- `__pycache__/` - Python緩存

## 🔧 工具說明

### `dev_clean.bat`
- 清理開發環境的用戶數據
- 清理緩存和臨時文件
- 準備乾淨的打包環境

### `build_production.bat`
- 自動清理開發環境
- 創建隔離的構建環境
- 只複製源代碼文件
- 安全檢查和驗證
- 生成分發包

### `check_exe_content.py`
- 檢查可執行文件內容
- 搜索用戶ID模式
- 驗證安全性

## 🚨 緊急情況

### 如果已經分發了包含個人數據的版本
1. **立即停止分發**
2. **通知所有用戶停止使用**
3. **重新構建乾淨版本**
4. **更新分發渠道**

### 如果發現洩露
1. **評估洩露範圍**
2. **更改相關密碼**
3. **檢查Facebook安全設置**
4. **改進開發流程**

## 💡 最佳實踐

### 開發環境
- 使用專門的測試Facebook帳號
- 定期清理開發環境
- 不要在生產環境測試

### 版本控制
- 添加 `.gitignore` 排除用戶數據
- 不要提交個人信息
- 定期檢查提交內容

### 打包分發
- 始終使用生產打包腳本
- 每次打包前清理環境
- 驗證每個分發版本

## 📞 故障排除

### 如果清理腳本失敗
1. 手動檢查並刪除用戶數據文件
2. 重啟電腦清理內存
3. 檢查文件權限

### 如果仍然包含用戶數據
1. 檢查是否有隱藏的數據文件
2. 搜索代碼中的硬編碼信息
3. 使用更嚴格的檢查工具

## 🎯 目標

確保分發的軟件：
- ✅ 不包含任何個人信息
- ✅ 不包含開發者的測試數據
- ✅ 可以安全地分發給任何人
- ✅ 保護用戶和開發者的隱私
