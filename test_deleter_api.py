#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試刪除API的問題
"""

import requests
import json

def test_deleter_api():
    """測試刪除API"""
    print("測試刪除API...")
    print("=" * 50)
    
    try:
        # 測試刪除API
        url = "http://localhost:5000/api/deleter/start"
        data = {
            "delete_all": True,
            "target_tags": []
        }
        
        print(f"發送請求到: {url}")
        print(f"請求數據: {data}")
        
        response = requests.post(url, json=data)
        
        print(f"響應狀態碼: {response.status_code}")
        print(f"響應內容: {response.text}")
        
        if response.status_code == 400:
            print("❌ 400錯誤 - 請求參數問題")
        elif response.status_code == 401:
            print("❌ 401錯誤 - 未登入Facebook")
        elif response.status_code == 403:
            print("❌ 403錯誤 - 權限不足")
        elif response.status_code == 500:
            print("❌ 500錯誤 - 服務器內部錯誤")
        elif response.status_code == 200:
            print("✅ 請求成功")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    test_deleter_api()
