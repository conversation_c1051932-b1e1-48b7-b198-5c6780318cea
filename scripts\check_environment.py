#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
環境檢查腳本
檢查 Facebook 工具運行所需的環境和依賴
"""

import sys
import subprocess
import os
import platform

def check_python_version():
    """檢查 Python 版本"""
    print("🐍 檢查 Python 版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - 版本符合要求")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - 需要 Python 3.7 或更高版本")
        return False

def check_pip():
    """檢查 pip 是否可用"""
    print("\n📦 檢查 pip...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ pip 可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ pip 不可用")
            return False
    except Exception as e:
        print(f"❌ pip 檢查失敗: {e}")
        return False

def check_required_packages():
    """檢查必需的 Python 包"""
    print("\n📚 檢查必需的 Python 包...")
    required_packages = [
        "flask",
        "flask_cors", 
        "DrissionPage"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安裝")
        except ImportError:
            print(f"❌ {package} - 未安裝")
            missing_packages.append(package)
    
    return missing_packages

def check_chrome_browser():
    """檢查 Chrome 瀏覽器"""
    print("\n🌐 檢查 Chrome 瀏覽器...")
    system = platform.system()
    
    chrome_paths = {
        "Windows": [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ],
        "Darwin": [  # macOS
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        ],
        "Linux": [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser"
        ]
    }
    
    if system in chrome_paths:
        for path in chrome_paths[system]:
            if os.path.exists(path):
                print(f"✅ Chrome 瀏覽器已安裝: {path}")
                return True
    
    # 嘗試命令行檢查
    try:
        if system == "Windows":
            result = subprocess.run(["where", "chrome"], capture_output=True, text=True)
        else:
            result = subprocess.run(["which", "google-chrome"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Chrome 瀏覽器已安裝: {result.stdout.strip()}")
            return True
    except:
        pass
    
    print("❌ 未找到 Chrome 瀏覽器")
    print("   請從 https://www.google.com/chrome/ 下載並安裝")
    return False

def check_project_files():
    """檢查項目文件"""
    print("\n📁 檢查項目文件...")
    required_files = [
        "app.py",
        "facebook_deleter_with_urls.py", 
        "facebook_groups_crawler.py",
        "requirements.txt",
        "templates/index.html"
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - 存在")
        else:
            print(f"❌ {file} - 缺失")
            missing_files.append(file)
    
    return missing_files

def install_missing_packages(missing_packages):
    """安裝缺失的包"""
    if not missing_packages:
        return True
    
    print(f"\n📥 嘗試安裝缺失的包: {', '.join(missing_packages)}")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 依賴安裝成功")
            return True
        else:
            print(f"❌ 依賴安裝失敗: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安裝過程中發生錯誤: {e}")
        return False

def main():
    """主檢查函數"""
    print("=" * 60)
    print("🔍 Facebook 工具環境檢查")
    print("=" * 60)
    
    all_good = True
    
    # 檢查 Python 版本
    if not check_python_version():
        all_good = False
    
    # 檢查 pip
    if not check_pip():
        all_good = False
    
    # 檢查項目文件
    missing_files = check_project_files()
    if missing_files:
        all_good = False
        print(f"\n❌ 缺失重要文件，請確保所有項目文件都已複製到當前目錄")
    
    # 檢查 Python 包
    missing_packages = check_required_packages()
    if missing_packages:
        print(f"\n📥 發現缺失的包，嘗試自動安裝...")
        if install_missing_packages(missing_packages):
            # 重新檢查
            missing_packages = check_required_packages()
            if missing_packages:
                all_good = False
        else:
            all_good = False
    
    # 檢查 Chrome
    if not check_chrome_browser():
        all_good = False
    
    # 總結
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 環境檢查完成！所有要求都已滿足")
        print("✅ 您可以運行 start_server.bat (Windows) 或 start_server.sh (Linux/Mac) 來啟動工具")
    else:
        print("⚠️ 環境檢查發現問題，請根據上述提示解決後重新檢查")
        print("📖 詳細部署指南請參考 DEPLOYMENT_GUIDE.md")
    print("=" * 60)

if __name__ == "__main__":
    main()
