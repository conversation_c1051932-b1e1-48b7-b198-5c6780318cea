# Facebook Tool 項目結構

## 📁 資料夾結構

```
deleter_toolkit/
├── auth/                    # 🔐 驗證系統
│   ├── __init__.py         # Python包初始化
│   ├── .env                # 環境變數配置
│   ├── auth_system.py      # 核心授權系統
│   ├── create_auth_code.py # 創建授權碼工具
│   ├── device_manager.py   # 設備管理工具
│   ├── usage_monitor.py    # 使用監控工具
│   ├── test_anti_abuse.py  # 防濫用測試
│   ├── test_supabase.py    # Supabase連接測試
│   ├── manage_auto_bind.py # 自動綁定管理
│   └── run_auth_tools.bat  # 驗證工具啟動器
│
├── scripts/                # 🔧 腳本工具
│   ├── archive/            # 📦 無效BAT文件歸檔
│   ├── clean_dev.bat       # 開發環境清理
│   ├── build_production.bat # 生產環境打包
│   ├── start.bat           # 應用啟動腳本
│   ├── check_environment.py # 環境檢查
│   ├── check_exe_content.py # 執行檔內容檢查
│   ├── test_runtime_files.py # 運行時文件測試
│   └── setup.py            # 安裝腳本
│
├── templates/              # 🌐 前端模板
│   └── index.html          # 主頁面
│
├── static/                 # 📁 靜態資源
├── release/                # 🚀 發布文件
├── __pycache__/            # Python緩存
│
├── app.py                  # 🎯 主應用程序
├── facebook_deleter_with_urls.py # Facebook刪除功能
├── facebook_groups_crawler.py    # Facebook社團抓取
├── requirements.txt        # Python依賴
└── 各種文檔.md            # 項目文檔
```

## 🔐 驗證系統 (auth/)

### 核心文件
- **auth_system.py**: 設備綁定、Facebook ID驗證、防濫用機制
- **.env**: Supabase連接配置

### 管理工具
- **create_auth_code.py**: 創建授權碼
- **device_manager.py**: 管理設備綁定
- **usage_monitor.py**: 監控使用情況

### 測試工具
- **test_supabase.py**: 測試Supabase連接
- **test_anti_abuse.py**: 測試防濫用機制

## 🔧 腳本工具 (scripts/)

### 有效腳本
- **clean_dev.bat**: 清理開發環境
- **build_production.bat**: 生產環境打包
- **start.bat**: 啟動應用

### 歸檔文件 (scripts/archive/)
- 各種無效的BAT文件已歸檔，不再使用

## 🚀 使用說明

### 啟動驗證管理
```bash
cd auth
run_auth_tools.bat
```

### 啟動主應用
```bash
python app.py
```

### 生產打包
```bash
scripts\build_production.bat
```

## 📊 資料存儲

所有驗證資料存儲在Supabase PostgreSQL資料庫：
- 設備綁定記錄
- Facebook ID綁定
- 使用日誌
- 授權碼管理

## 🛡️ 安全機制

1. **設備綁定**: 一設備一Facebook ID
2. **授權碼控制**: 需要有效授權碼才能綁定
3. **時效限制**: 授權有有效期
4. **使用監控**: 記錄所有操作和濫用嘗試
