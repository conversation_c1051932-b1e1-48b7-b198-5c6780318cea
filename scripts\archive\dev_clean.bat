@echo off
echo Development Environment Cleanup
echo ================================

echo Step 1: Stop all Python processes
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Step 2: Clear all user data files
for %%f in (urls.txt crawl_info.json facebook_deleter.log) do (
    if exist "%%f" (
        echo Deleting %%f
        del /f /q "%%f"
    )
)

echo Step 3: Clear temp directories
for %%d in ("%TEMP%" "%USERPROFILE%\AppData\Local\Temp") do (
    for %%f in (urls.txt crawl_info.json facebook_deleter.log) do (
        if exist "%%d\%%f" (
            echo Deleting %%d\%%f
            del /f /q "%%d\%%f"
        )
    )
)

echo Step 4: Clear Python cache
for %%d in (__pycache__ build dist .pytest_cache) do (
    if exist "%%d" (
        echo Removing %%d
        rmdir /s /q "%%d"
    )
)

echo Step 5: Clear PyInstaller cache
if exist "%USERPROFILE%\.pyinstaller" rmdir /s /q "%USERPROFILE%\.pyinstaller"

echo Step 6: Reset backend memory - restart required
echo NOTE: Please restart the application to clear backend memory

echo.
echo Development environment cleaned!
echo Remember to restart the app to clear memory
echo.
pause
