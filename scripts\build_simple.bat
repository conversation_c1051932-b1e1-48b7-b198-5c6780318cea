@echo off
echo Simple Facebook Tool Build
echo ===========================

echo Step 1: Clean environment
call clean_dev.bat

echo Step 2: Install dependencies
pip install pyinstaller

echo Step 3: Build executable
pyinstaller --onefile --console ^
    --add-data "templates;templates" ^
    --add-data "auth;auth" ^
    --exclude-module tkinter ^
    --exclude-module matplotlib ^
    --exclude-module numpy ^
    --exclude-module pandas ^
    --name FacebookTool_Simple ^
    app.py

echo Step 4: Check build result
if exist "dist\FacebookTool_Simple.exe" (
    echo ✅ Build successful!
    
    echo Step 5: Security check
    python check_exe_content.py "dist\FacebookTool_Simple.exe"
    
    echo Step 6: Move to release
    if not exist "..\release" mkdir "..\release"
    copy "dist\FacebookTool_Simple.exe" "..\release\"
    copy "start_simple_template.bat" "..\release\start_facebook_tool.bat"

    echo ✅ Build completed!
    echo Executable: release\FacebookTool_Simple.exe
    echo Launcher: release\start_facebook_tool.bat
) else (
    echo ❌ Build failed!
)

pause
