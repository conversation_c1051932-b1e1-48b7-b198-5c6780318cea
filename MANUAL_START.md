# 手動啟動指南

如果所有批處理文件都有問題，請按照以下步驟手動啟動：

## Windows 用戶

1. **打開命令提示字元**
   - 按 `Win + R`
   - 輸入 `cmd` 並按 Enter

2. **切換到項目目錄**
   ```cmd
   cd C:\path\to\your\facebook_tool
   ```

3. **安裝依賴**
   ```cmd
   pip install Flask
   pip install Flask-CORS
   pip install DrissionPage
   ```

4. **啟動服務器**
   ```cmd
   python app.py
   ```

5. **訪問網頁**
   - 打開瀏覽器
   - 訪問 `http://localhost:5000`

## 如果 python 命令不工作

嘗試使用 `py` 命令：

```cmd
py -m pip install Flask Flask-CORS DrissionPage
py app.py
```

## 如果 pip 不工作

```cmd
python -m pip install Flask Flask-CORS DrissionPage
```

## 完整的一行命令

```cmd
pip install Flask Flask-CORS DrissionPage && python app.py
```

## 檢查 Python 是否安裝

```cmd
python --version
```

如果顯示版本號，說明 Python 已安裝。
如果顯示錯誤，請先安裝 Python：https://python.org

## 常見錯誤解決

1. **'python' 不是內部或外部命令**
   - 重新安裝 Python，勾選 "Add Python to PATH"

2. **'pip' 不是內部或外部命令**
   - 使用 `python -m pip` 代替 `pip`

3. **權限錯誤**
   - 以管理員身份運行命令提示字元

4. **網路錯誤**
   - 檢查網路連接
   - 嘗試使用國內鏡像：
     ```cmd
     pip install -i https://pypi.tuna.tsinghua.edu.cn/simple Flask Flask-CORS DrissionPage
     ```
