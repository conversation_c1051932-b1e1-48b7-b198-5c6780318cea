#!/usr/bin/env python3
"""
Facebook 全部貼文刪除器 - DrissionPage版本
使用DrissionPage控制瀏覽器，支援從txt文件載入網址清單
⚠️ 注意：此版本會刪除所有可見的貼文，不進行標籤篩選

使用方法:
1. 安裝依賴: pip install DrissionPage
2. 準備網址清單文件 urls.txt
3. 修改配置區域的設定
4. 運行腳本: python facebook_deleter_with_urls.py
"""

import time
import logging
import os
from typing import List, Dict
from DrissionPage import ChromiumPage, ChromiumOptions

# 設定日誌 - 使用臨時目錄避免打包時包含用戶數據
import tempfile
log_file = os.path.join(tempfile.gettempdir(), 'facebook_deleter.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FacebookDrissionDeleter:
    """Facebook 全部貼文刪除器 - DrissionPage版本"""
    
    def __init__(self, headless: bool = False):
        self.page = None
        self.headless = headless
        
    def setup_browser(self, use_existing_browser: bool = True):
        """設定瀏覽器"""
        try:
            if use_existing_browser:
                # 嘗試連接到現有的瀏覽器實例
                try:
                    logger.info("嘗試連接到現有的瀏覽器實例...")
                    self.page = ChromiumPage()  # 不傳入選項，直接連接現有實例

                    # 測試連接是否成功
                    current_url = self.page.url
                    logger.info(f"成功連接到現有瀏覽器，當前頁面: {current_url}")
                    return True

                except Exception as e:
                    logger.warning(f"連接現有瀏覽器失敗: {e}")
                    logger.info("將創建新的瀏覽器實例...")

            # 創建新的瀏覽器實例
            options = ChromiumOptions()
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-blink-features=AutomationControlled')
            options.set_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            if self.headless:
                options.headless()

            # 創建頁面實例
            self.page = ChromiumPage(addr_or_opts=options)

            logger.info("新瀏覽器實例創建完成")
            return True

        except Exception as e:
            logger.error(f"設定瀏覽器失敗: {e}")
            return False
    
    def check_facebook_login_status(self) -> bool:
        """檢查Facebook登入狀態"""
        try:
            logger.info("檢查Facebook登入狀態...")

            # 先嘗試訪問Facebook首頁
            self.page.get("https://www.facebook.com")
            time.sleep(3)

            current_url = self.page.url
            logger.info(f"當前頁面URL: {current_url}")

            # 檢查是否已登入
            if "login" in current_url.lower():
                logger.error("瀏覽器未登入Facebook，請先手動登入")
                return False

            # 檢查是否有登入用戶的標識元素
            try:
                # 尋找用戶頭像或個人檔案按鈕
                profile_elements = [
                    'div[aria-label*="你的個人檔案"]',
                    'div[aria-label*="個人檔案"]',
                    'a[aria-label*="個人檔案"]',
                    'img[alt*="你的個人檔案"]'
                ]

                for selector in profile_elements:
                    element = self.page.ele(selector, timeout=2)
                    if element:
                        logger.info("檢測到已登入Facebook")
                        return True

                # 如果找不到個人檔案元素，但URL不包含login，可能仍然是登入狀態
                if "facebook.com" in current_url and "login" not in current_url:
                    logger.info("Facebook可能已登入（基於URL判斷）")
                    return True

                logger.error("無法確認Facebook登入狀態")
                return False

            except Exception as e:
                logger.warning(f"檢查登入狀態時發生錯誤: {e}")
                # 如果檢查過程出錯，但URL顯示在Facebook且不是登入頁面，假設已登入
                if "facebook.com" in current_url and "login" not in current_url:
                    logger.info("假設Facebook已登入（檢查過程出錯但URL正常）")
                    return True
                return False

        except Exception as e:
            logger.error(f"檢查Facebook登入狀態失敗: {e}")
            return False
    
    def load_urls_from_file(self, file_path: str) -> List[str]:
        """從txt文件載入網址清單"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"網址清單文件不存在: {file_path}")
                print(f"❌ 找不到網址清單文件: {file_path}")
                print(f"💡 請確保 {file_path} 文件存在於腳本同一目錄下")
                return []

            urls = []
            valid_count = 0
            invalid_count = 0

            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    url = line.strip()

                    # 跳過空行
                    if not url:
                        continue

                    # 跳過註解行
                    if url.startswith('#'):
                        continue

                    # 檢查是否為有效的Facebook群組網址
                    if url.startswith('https://www.facebook.com/groups/') and '/user/' in url:
                        urls.append(url)
                        valid_count += 1
                        if valid_count <= 5:  # 只顯示前5個網址
                            logger.debug(f"載入網址 {valid_count}: {url}")
                    else:
                        invalid_count += 1
                        if invalid_count <= 3:  # 只顯示前3個無效網址
                            logger.warning(f"第 {line_num} 行不是有效的Facebook群組網址: {url}")

            logger.info(f"從 {file_path} 載入了 {valid_count} 個有效網址")
            if invalid_count > 0:
                logger.warning(f"跳過了 {invalid_count} 個無效網址")

            print(f"📋 成功載入 {valid_count} 個社團網址")
            if invalid_count > 0:
                print(f"⚠️ 跳過 {invalid_count} 個無效網址")

            return urls

        except Exception as e:
            logger.error(f"載入網址清單失敗: {e}")
            print(f"❌ 載入網址清單失敗: {e}")
            return []
    
    def delete_all_posts(self, group_user_urls: List[str], progress_callback=None) -> Dict:
        """
        刪除所有貼文（全部刪除功能）
        
        Args:
            group_user_urls: 社團用戶個人動態頁面URL列表
            progress_callback: 進度回調函數

        Returns:
            Dict: 刪除結果
        """
        try:
            logger.info("開始全部刪除模式")
            logger.info(f"共需處理 {len(group_user_urls)} 個社團")
            
            total_deleted = 0
            processed_groups = 0
            
            for i, user_url in enumerate(group_user_urls):
                try:
                    # 更新進度
                    progress = 40 + int((i / len(group_user_urls)) * 50)  # 40-90%
                    if progress_callback:
                        progress_callback(progress, f"處理第 {i+1}/{len(group_user_urls)} 個社團...")

                    logger.info(f"處理第 {i+1}/{len(group_user_urls)} 個社團: {user_url}")

                    # 跳轉到用戶個人動態頁面
                    self.page.get(user_url)
                    time.sleep(3)
                    
                    # 檢查頁面是否正常載入
                    if "login" in self.page.url.lower():
                        logger.warning(f"頁面重定向到登入頁面，跳過: {user_url}")
                        continue
                    
                    # 在該頁面中刪除所有可見的貼文
                    deleted_count = self._delete_all_posts_in_current_page()
                    
                    total_deleted += deleted_count
                    processed_groups += 1
                    
                    logger.info(f"在第 {i+1} 個社團中刪除了 {deleted_count} 個貼文")
                    
                    # 避免操作過於頻繁
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"處理社團 {i+1} 時發生錯誤: {e}")
                    continue

            # 最終進度更新
            if progress_callback:
                progress_callback(95, "完成所有社團處理...")

            result = {
                'success': True,
                'message': f'完成全部刪除，共處理 {processed_groups} 個社團，刪除 {total_deleted} 個貼文',
                'total_deleted': total_deleted,
                'processed_groups': processed_groups
            }
            
            logger.info(result['message'])
            return result
            
        except Exception as e:
            logger.error(f"全部刪除失敗: {e}")
            return {'success': False, 'message': f'全部刪除失敗: {str(e)}'}
    
    def delete_posts_with_tags(self, group_user_urls: List[str], target_tags: List[str], progress_callback=None) -> Dict:
        """
        針對性刪除功能 - 刪除包含特定標籤組合的貼文

        Args:
            group_user_urls: 社團用戶個人動態頁面URL列表
            target_tags: 目標標籤列表（需要同時包含所有標籤）
            progress_callback: 進度回調函數

        Returns:
            Dict: 刪除結果
        """
        try:
            logger.info(f"開始針對性刪除，目標標籤: {target_tags}")
            logger.info(f"共需處理 {len(group_user_urls)} 個社團")

            if progress_callback:
                progress_callback(5, f"開始針對性刪除，目標標籤: {target_tags}")

            total_deleted = 0
            processed_groups = 0

            for i, user_url in enumerate(group_user_urls):
                try:
                    logger.info(f"處理第 {i+1}/{len(group_user_urls)} 個社團: {user_url}")

                    if progress_callback:
                        progress = 10 + (i / len(group_user_urls)) * 80
                        progress_callback(int(progress), f"處理第 {i+1}/{len(group_user_urls)} 個社團")

                    # 跳轉到用戶個人動態頁面
                    self.page.get(user_url)
                    time.sleep(3)

                    # 檢查頁面是否正常載入
                    if "login" in self.page.url.lower():
                        logger.warning(f"頁面重定向到登入頁面，跳過: {user_url}")
                        continue

                    # 在該頁面中搜尋並刪除包含目標標籤的貼文
                    deleted_count = self._delete_posts_in_current_page(target_tags)

                    total_deleted += deleted_count
                    processed_groups += 1

                    logger.info(f"在第 {i+1} 個社團中刪除了 {deleted_count} 個貼文")

                    # 避免操作過於頻繁
                    time.sleep(2)

                except Exception as e:
                    logger.error(f"處理社團 {i+1} 時發生錯誤: {e}")
                    continue

            if progress_callback:
                progress_callback(95, "完成所有社團處理...")

            result = {
                'success': True,
                'message': f'完成針對性刪除，共處理 {processed_groups} 個社團，刪除 {total_deleted} 個貼文',
                'total_deleted': total_deleted,
                'processed_groups': processed_groups,
                'target_tags': target_tags
            }

            logger.info(result['message'])
            return result

        except Exception as e:
            logger.error(f"針對性刪除失敗: {e}")
            return {'success': False, 'message': f'針對性刪除失敗: {str(e)}'}
    
    def _delete_posts_in_current_page(self, target_tags: List[str]) -> int:
        """在當前頁面中刪除包含目標標籤的貼文 - 優化滾動和載入"""
        deleted_count = 0
        consecutive_failures = 0
        max_consecutive_failures = 10  # 增加失敗次數限制
        scroll_attempts = 0
        max_scroll_attempts = 999  # 移除滾動次數限制，直到到達頁面底部

        logger.info("🔄 開始完整滾動載入貼文（直到頁面底部）...")

        # 記錄初始狀態
        initial_containers = self.page.run_js("return document.querySelectorAll('[role=\"article\"], div[data-ad-preview=\"message\"]').length;")
        logger.info(f"📊 初始容器數量: {initial_containers}")

        no_new_content_count = 0  # 連續沒有新內容的次數
        last_container_count = initial_containers

        while scroll_attempts < max_scroll_attempts:
            try:
                scroll_attempts += 1

                # 使用簡單的滾動策略
                scroll_info = self.page.run_js("""
                var before = window.pageYOffset;
                var pageHeight = document.body.scrollHeight;
                var viewportHeight = window.innerHeight;

                window.scrollBy(0, 600);  // 每次滾動600像素

                var after = window.pageYOffset;
                var atBottom = (after + viewportHeight) >= (pageHeight - 50);  // 50px容差

                return {
                    before: before,
                    after: after,
                    pageHeight: pageHeight,
                    viewportHeight: viewportHeight,
                    atBottom: atBottom,
                    scrolled: after > before
                };
                """)

                logger.info(f"📜 第 {scroll_attempts} 次滾動: {scroll_info['before']:.0f} -> {scroll_info['after']:.0f}")

                # 等待內容載入
                time.sleep(2)

                # 檢查是否有新內容載入
                current_containers = self.page.run_js("return document.querySelectorAll('[role=\"article\"], div[data-ad-preview=\"message\"]').length;")

                if current_containers > last_container_count:
                    logger.info(f"📈 載入了新內容: {last_container_count} -> {current_containers} 個容器")
                    last_container_count = current_containers
                    no_new_content_count = 0  # 重置計數
                else:
                    no_new_content_count += 1
                    logger.debug(f"沒有新內容載入 ({no_new_content_count}/3)")

                # 檢查是否到達底部
                if scroll_info['atBottom'] or not scroll_info['scrolled']:
                    logger.info("📄 已到達頁面底部")
                    if no_new_content_count >= 3:
                        logger.info("📄 確認沒有更多內容，停止滾動")
                        break
                    else:
                        logger.info("⏳ 等待更多內容載入...")
                        time.sleep(3)  # 額外等待時間
                        continue

                # 使用成功測試的JavaScript刪除邏輯
                delete_result = self._find_and_delete_with_js(target_tags)

                if delete_result:
                    # 找到目標貼文，JavaScript已經完成刪除
                    logger.info("🎯 JavaScript已完成目標貼文的刪除操作")

                    deleted_count += 1
                    consecutive_failures = 0
                    logger.info(f"✅ 成功刪除第 {deleted_count} 個包含目標標籤的貼文")

                    # 刪除後等待頁面更新，但不重置滾動計數
                    logger.info("⏳ 等待頁面更新...")
                    time.sleep(6)  # 等待頁面更新
                    consecutive_failures = 0

                    # 更新容器計數（因為刪除了貼文）
                    last_container_count = self.page.run_js("return document.querySelectorAll('[role=\"article\"], div[data-ad-preview=\"message\"]').length;")
                    logger.info(f"📊 刪除後容器數量: {last_container_count}")

                    # 繼續當前的滾動進度，不重新開始
                    continue
                else:
                    # 沒找到目標貼文，繼續滾動
                    consecutive_failures += 1
                    logger.debug(f"第 {scroll_attempts} 次滾動後未找到目標貼文")

            except KeyboardInterrupt:
                logger.info("用戶中斷操作")
                break
            except Exception as e:
                logger.warning(f"⚠️ 刪除過程中發生異常: {e}")
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    break
                continue

        logger.info(f"📊 搜尋完成，共滾動 {scroll_attempts} 次，刪除 {deleted_count} 個貼文")
        return deleted_count

    def _find_and_delete_with_js(self, target_tags: List[str]) -> bool:
        """使用成功測試的JavaScript邏輯搜尋並刪除目標貼文"""
        try:
            logger.info(f"🔍 使用JavaScript搜尋目標標籤: {target_tags}")

            # 使用成功驗證的JavaScript刪除邏輯
            result = self.page.run_js(f"""
            console.log('🔍 開始搜尋目標貼文...');

            var targetTags = {target_tags};
            var containers = document.querySelectorAll('[role="article"], div[data-ad-preview="message"]');

            console.log('檢查', containers.length, '個容器');

            for (var i = 0; i < containers.length; i++) {{
                var container = containers[i];
                var hashtags = [];
                var links = container.querySelectorAll('a[href*="hashtag"]');

                links.forEach(function(link) {{
                    var text = link.textContent.trim();
                    if (text.startsWith('#')) {{
                        hashtags.push(text);
                    }}
                }});

                // 檢查是否包含所有目標標籤
                var hasAllTags = targetTags.every(function(targetTag) {{
                    return hashtags.some(function(hashtag) {{
                        var targetClean = targetTag.toLowerCase().replace('#', '');
                        var hashtagClean = hashtag.toLowerCase().replace('#', '');
                        return hashtagClean.includes(targetClean) || targetClean.includes(hashtagClean);
                    }});
                }});

                if (hasAllTags) {{
                    console.log('🎉 找到目標貼文! 容器', i, '標籤:', hashtags);

                    // 滾動到容器
                    container.scrollIntoView({{
                        behavior: 'smooth',
                        block: 'center'
                    }});

                    // 等待滾動完成，然後搜尋操作按鈕
                    setTimeout(function() {{
                        console.log('📍 滾動完成，開始全頁面搜尋操作按鈕...');

                        // 使用成功驗證的全頁面搜尋策略
                        var globalButtons = document.querySelectorAll('div[aria-label="可對此貼文採取的動作"][role="button"]');
                        console.log('全頁面找到', globalButtons.length, '個操作按鈕');

                        // 找到靠近當前容器的按鈕
                        var containerRect = container.getBoundingClientRect();
                        var nearbyButton = null;
                        var minDistance = Infinity;

                        for (var btnIdx = 0; btnIdx < globalButtons.length; btnIdx++) {{
                            var btn = globalButtons[btnIdx];
                            var btnRect = btn.getBoundingClientRect();
                            var distance = Math.abs(btnRect.top - containerRect.top);

                            if (distance < 200 && btn.offsetParent !== null && distance < minDistance) {{
                                nearbyButton = btn;
                                minDistance = distance;
                            }}
                        }}

                        if (nearbyButton) {{
                            console.log('🔘 點擊操作按鈕...');
                            nearbyButton.click();

                            // 等待選單出現
                            setTimeout(function() {{
                                console.log('🔍 搜尋刪除選項...');

                                var menuItems = document.querySelectorAll('div[role="menuitem"]');
                                var deleteItem = null;

                                for (var j = 0; j < menuItems.length; j++) {{
                                    var item = menuItems[j];
                                    var text = item.textContent.trim();

                                    if (text === '刪除貼文' && item.offsetParent !== null) {{
                                        deleteItem = item;
                                        break;
                                    }}
                                }}

                                if (deleteItem) {{
                                    console.log('🗑️ 點擊刪除選項...');
                                    deleteItem.click();

                                    // 等待確認對話框
                                    setTimeout(function() {{
                                        console.log('🔍 搜尋確認按鈕...');

                                        var confirmButtons = document.querySelectorAll('div[role="button"], button');
                                        var confirmButton = null;

                                        for (var k = 0; k < confirmButtons.length; k++) {{
                                            var btn = confirmButtons[k];
                                            var text = btn.textContent.trim();

                                            if ((text === '刪除' || text === 'Delete') &&
                                                btn.offsetParent !== null) {{
                                                confirmButton = btn;
                                                break;
                                            }}
                                        }}

                                        if (confirmButton) {{
                                            console.log('✅ 點擊確認按鈕...');
                                            confirmButton.click();
                                            console.log('🎉 刪除流程完成!');
                                        }}
                                    }}, 1500);
                                }}
                            }}, 1500);
                        }}
                    }}, 2000);

                    return 'processing';
                }}
            }}

            return 'not_found';
            """)

            if result == 'processing':
                logger.info("✅ JavaScript開始處理目標貼文")
                return True
            else:
                logger.debug("❌ JavaScript未找到目標貼文")
                return False

        except Exception as e:
            logger.error(f"JavaScript刪除失敗: {e}")
            return False

    def _post_contains_all_tags(self, post_element, target_tags: List[str]) -> bool:
        """檢查貼文是否包含所有目標標籤"""
        try:
            # 獲取貼文的所有文本內容
            post_text = post_element.text.lower() if post_element.text else ""

            # 檢查是否包含所有標籤
            for tag in target_tags:
                if not self._text_contains_tag(post_text, tag):
                    return False

            logger.debug(f"✅ 貼文包含所有目標標籤: {target_tags}")
            return True

        except Exception as e:
            logger.warning(f"檢查貼文標籤時發生錯誤: {e}")
            return False

    def _text_contains_tag(self, text: str, target_tag: str) -> bool:
        """檢查文本是否包含標籤"""
        target_lower = target_tag.lower().strip()

        # 嘗試多種格式
        search_formats = [
            target_lower,
            target_lower.replace('#', ''),
            '#' + target_lower.replace('#', ''),
            target_lower.replace(' ', ''),
            target_lower.replace('#', '').replace(' ', '')
        ]

        for format_str in search_formats:
            if format_str in text:
                return True

        return False

    def _delete_single_post_with_tags(self, post_element) -> bool:
        """刪除單個包含標籤的貼文"""
        try:
            logger.info("🎯 嘗試刪除匹配的貼文...")

            # 在貼文容器中找三個點按鈕
            three_dots_btn = post_element.s_ele('div[role="button"][aria-label*="動作"]')
            if not three_dots_btn:
                # 嘗試其他可能的選擇器
                three_dots_btn = post_element.s_ele('div[aria-haspopup="true"]')

            if not three_dots_btn:
                logger.warning("❌ 在貼文中找不到三個點按鈕")
                return False

            # 使用現有的刪除邏輯
            return self._delete_single_post_simple(three_dots_btn.raw)

        except Exception as e:
            logger.error(f"刪除單個貼文失敗: {e}")
            return False
    
    def _delete_all_posts_in_current_page(self) -> int:
        """在當前頁面中刪除所有可見的貼文 - 簡化版本，重點在刪除而非滾動"""
        deleted_count = 0
        consecutive_failures = 0
        max_consecutive_failures = 5  # 減少失敗次數限制
        attempts = 0
        max_attempts = 50  # 限制總嘗試次數

        logger.info("🔄 開始全部刪除模式，重點刪除當前可見貼文...")

        while attempts < max_attempts and consecutive_failures < max_consecutive_failures:
            try:
                attempts += 1
                logger.info(f"🔍 第 {attempts} 次嘗試刪除...")

                # 嘗試刪除當前可見的任何貼文
                delete_result = self._find_and_delete_any_post()

                if delete_result:
                    deleted_count += 1
                    consecutive_failures = 0
                    logger.info(f"✅ 成功刪除第 {deleted_count} 個貼文")

                    # 刪除後等待頁面更新
                    logger.info("⏳ 等待頁面更新...")
                    time.sleep(4)  # 減少等待時間

                    # 輕微滾動以載入新內容
                    self.page.run_js("window.scrollBy(0, 300);")
                    time.sleep(2)

                else:
                    consecutive_failures += 1
                    logger.debug(f"第 {attempts} 次嘗試未找到可刪除的貼文 (連續失敗: {consecutive_failures})")

                    # 如果連續失敗，嘗試滾動載入更多內容
                    if consecutive_failures >= 2:
                        logger.info("🔄 連續失敗，嘗試滾動載入更多內容...")
                        scroll_result = self.page.run_js("""
                        var before = window.pageYOffset;
                        window.scrollBy(0, 800);
                        var after = window.pageYOffset;
                        return after > before;
                        """)

                        if not scroll_result:
                            logger.info("📄 無法繼續滾動，可能已到達頁面底部")
                            break

                        time.sleep(3)  # 等待新內容載入

            except KeyboardInterrupt:
                logger.info("用戶中斷操作")
                break
            except Exception as e:
                logger.warning(f"⚠️ 刪除過程中發生異常: {e}")
                consecutive_failures += 1
                continue

        logger.info(f"📊 全部刪除完成，共嘗試 {attempts} 次，刪除 {deleted_count} 個貼文")
        return deleted_count
    
    def _find_and_delete_any_post(self) -> bool:
        """找到並刪除任何可見的貼文（全部刪除模式）- 直接搜尋三個點按鈕"""
        try:
            logger.info("🔍 搜尋當前可見的任何可刪除貼文...")
            logger.info("� 直接搜尋三個點按鈕，不依賴容器...")

            # 直接使用 JavaScript 完成整個刪除流程
            logger.info("� 嘗試用 JavaScript 直接找到並點擊按鈕...")
            js_result = self.page.run_js("""
                console.log('🔍 JavaScript 直接搜尋三個點按鈕...');

                // 搜尋所有可能的三個點按鈕
                var buttons = document.querySelectorAll('[aria-haspopup][aria-label*="動作"]');
                console.log('找到', buttons.length, '個 aria-haspopup 按鈕');

                for (var i = 0; i < buttons.length; i++) {
                    var btn = buttons[i];
                    console.log('按鈕', i+1, ':', btn.getAttribute('aria-label'));

                    if (btn.getAttribute('aria-label').includes('可對此貼文採取的動作')) {
                        console.log('✅ 找到目標按鈕，嘗試點擊...');
                        btn.scrollIntoView({block: 'center'});
                        btn.click();
                        return true;
                    }
                }

                console.log('❌ JavaScript 也找不到可點擊的按鈕');
                return false;
                """)

            if js_result:
                    logger.info("✅ JavaScript 成功點擊了三個點按鈕！")
                    time.sleep(2)

                    # 使用 JavaScript 完成後續步驟
                    logger.info("🔍 使用 JavaScript 搜尋刪除選項...")
                    delete_result = self.page.run_js("""
                    console.log('🔍 搜尋刪除選項...');
                    var deleteKeywords = ['刪除貼文', '刪除', 'Delete post', 'Delete'];

                    for (var k = 0; k < deleteKeywords.length; k++) {
                        var deleteItems = document.querySelectorAll('div[role="menuitem"]');
                        for (var j = 0; j < deleteItems.length; j++) {
                            var item = deleteItems[j];
                            var text = item.textContent || item.innerText || '';
                            if (text.includes(deleteKeywords[k])) {
                                console.log('✅ 找到刪除選項:', text);
                                item.click();
                                return true;
                            }
                        }
                    }
                    return false;
                    """)

                    if delete_result:
                        logger.info("✅ JavaScript 找到並點擊刪除選項！")
                        time.sleep(1.5)

                        # 使用 JavaScript 搜尋確認按鈕
                        logger.info("🔍 使用 JavaScript 搜尋確認按鈕...")
                        confirm_result = self.page.run_js("""
                        console.log('🔍 搜尋確認按鈕...');
                        var confirmKeywords = ['刪除', 'Delete'];

                        for (var k = 0; k < confirmKeywords.length; k++) {
                            var confirmButtons = document.querySelectorAll('div[role="button"]');
                            for (var j = 0; j < confirmButtons.length; j++) {
                                var btn = confirmButtons[j];
                                var text = btn.textContent || btn.innerText || '';
                                if (text.trim() === confirmKeywords[k]) {
                                    console.log('✅ 找到確認按鈕:', text);
                                    btn.click();
                                    return true;
                                }
                            }
                        }
                        return false;
                        """)

                        if confirm_result:
                            logger.info("🎉 JavaScript 完整刪除流程成功！")
                            return True
                        else:
                            logger.warning("❌ JavaScript 找不到確認按鈕")
                            return False
                    else:
                        logger.warning("❌ JavaScript 找不到刪除選項")
                        return False

            else:
                logger.warning("❌ JavaScript 無法點擊三個點按鈕，嘗試傳統方法...")

            # 如果 JavaScript 方法失敗，使用傳統方法
            logger.info("🔄 JavaScript 方法失敗，回退到傳統搜尋方法...")
            all_buttons = self.page.s_eles('[aria-haspopup][aria-label*="動作"]')
            logger.info(f"📊 總共找到 {len(all_buttons)} 個可能的三個點按鈕")

            # 遍歷每個找到的按鈕，嘗試刪除
            for i, btn in enumerate(all_buttons):
                try:
                    logger.info(f"🎯 嘗試第 {i+1}/{len(all_buttons)} 個三個點按鈕...")

                    # 檢查按鈕是否可見
                    is_visible = self.page.run_js("""
                    var btn = arguments[0];
                    var rect = btn.getBoundingClientRect();
                    return rect.width > 0 && rect.height > 0 && btn.offsetParent !== null;
                    """, btn.raw)

                    if not is_visible:
                        logger.debug(f"❌ 第 {i+1} 個按鈕不可見，跳過")
                        continue

                    # 使用簡單點擊的刪除邏輯
                    if self._delete_single_post_simple(btn.raw):
                        logger.info("🎉 成功刪除一個貼文！")
                        return True
                    else:
                        logger.debug(f"❌ 第 {i+1} 個按鈕刪除失敗，繼續下一個")
                        continue

                except Exception as e:
                    logger.warning(f"⚠️ 處理第 {i+1} 個按鈕時發生錯誤: {e}")
                    continue

            logger.debug("❌ 所有按鈕都無法刪除")
            return False

        except Exception as e:
            logger.error(f"刪除貼文失敗: {e}")
            return False

    def cdp_mouse_click(self, ele):
        """用原生滑鼠座標點擊，提升三點/刪除/確認的點擊成功率"""
        # 取得元素在頁面上的中心座標
        rect = self.page.run_js("""
        let r = arguments[0].getBoundingClientRect();
        return {x: r.left + r.width/2, y: r.top + r.height/2};
        """, ele)
        # 滑鼠移動+點擊
        self.page.cdp.send('Input.dispatchMouseEvent', {
            'type': 'mouseMoved',
            'x': rect['x'],
            'y': rect['y'],
            'button': 'none'
        })
        time.sleep(0.1)
        self.page.cdp.send('Input.dispatchMouseEvent', {
            'type': 'mousePressed',
            'x': rect['x'],
            'y': rect['y'],
            'button': 'left',
            'clickCount': 1
        })
        time.sleep(0.1)
        self.page.cdp.send('Input.dispatchMouseEvent', {
            'type': 'mouseReleased',
            'x': rect['x'],
            'y': rect['y'],
            'button': 'left',
            'clickCount': 1
        })
        time.sleep(0.5)

    def _delete_single_post_simple(self, three_dots_element):
        """使用簡單點擊的刪除邏輯"""
        try:
            logger.info("🔘 使用簡單點擊方法...")

            # 確保三點在視野中
            self.page.run_js("arguments[0].scrollIntoView({behavior:'smooth',block:'center'});", three_dots_element)
            time.sleep(0.6)

            # 點擊三個點按鈕
            logger.info("🔘 點擊三個點按鈕...")
            self.page.run_js("arguments[0].click();", three_dots_element)
            time.sleep(1.5)

            # 找刪除選項
            logger.info("🔍 搜尋刪除選項...")
            for attempt in range(6):
                delete_btn = None
                for delete_text in ['刪除貼文', '刪除', 'Delete post', 'Delete']:
                    delete_btn = self.page.s_ele('div[role="menuitem"]', text=delete_text)
                    if delete_btn:
                        break

                if delete_btn:
                    logger.info(f"✅ 找到刪除選項（嘗試 {attempt + 1}）")
                    self.page.run_js("arguments[0].click();", delete_btn.raw)
                    time.sleep(1.2)

                    # 找確認按鈕
                    logger.info("🔍 搜尋確認按鈕...")
                    for confirm_attempt in range(6):
                        confirm_btn = None
                        for confirm_text in ['刪除', 'Delete']:
                            confirm_btn = self.page.s_ele('div[role="button"]', text=confirm_text)
                            if confirm_btn:
                                break

                        if confirm_btn:
                            logger.info(f"✅ 找到確認按鈕（嘗試 {confirm_attempt + 1}）")
                            self.page.run_js("arguments[0].click();", confirm_btn.raw)
                            time.sleep(1.2)
                            logger.info("🎉 刪除流程完成！")
                            return True
                        time.sleep(0.8)
                    logger.warning("❌ 找不到確認按鈕")
                    break
                time.sleep(0.8)

            logger.warning("❌ 找不到刪除選項")
            return False
        except Exception as e:
            logger.error(f"刪除單個貼文失敗: {e}")
            return False

    def _delete_single_post(self, three_dots_element):
        """使用 CDP 滑鼠點擊的刪除邏輯（備用）"""
        try:
            # 確保三點在視野中
            self.page.run_js("arguments[0].scrollIntoView({behavior:'smooth',block:'center'});", three_dots_element)
            time.sleep(0.6)
            self.cdp_mouse_click(three_dots_element)
            time.sleep(1.5)

            # 找刪除
            for _ in range(6):
                delete_btn = None
                for delete_text in ['刪除貼文', '刪除', 'Delete post', 'Delete']:
                    delete_btn = self.page.s_ele('div[role="menuitem"]', text=delete_text)
                    if delete_btn:
                        break

                if delete_btn:
                    self.cdp_mouse_click(delete_btn.raw)
                    time.sleep(1.2)
                    # 找確認
                    for _ in range(6):
                        confirm_btn = None
                        for confirm_text in ['刪除', 'Delete']:
                            confirm_btn = self.page.s_ele('div[role="button"]', text=confirm_text)
                            if confirm_btn:
                                break

                        if confirm_btn:
                            self.cdp_mouse_click(confirm_btn.raw)
                            time.sleep(1.2)
                            return True
                        time.sleep(0.8)
                    break
                time.sleep(0.8)
            return False
        except Exception as e:
            logger.error(f"刪除單個貼文失敗: {e}")
            return False

    def _debug_page_elements(self):
        """調試：輸出頁面上實際存在的元素"""
        try:
            logger.info("🔍 調試：檢查頁面上實際存在的元素...")

            debug_info = self.page.run_js("""
            console.log('🔍 調試頁面元素...');

            var info = {
                articles: document.querySelectorAll('[role="article"]').length,
                divs_with_aria_label: [],
                buttons_with_aria_label: [],
                common_classes: []
            };

            // 檢查所有有 aria-label 的 div
            var divsWithAriaLabel = document.querySelectorAll('div[aria-label]');
            for (var i = 0; i < Math.min(divsWithAriaLabel.length, 10); i++) {
                var div = divsWithAriaLabel[i];
                info.divs_with_aria_label.push({
                    aria_label: div.getAttribute('aria-label'),
                    tag: div.tagName,
                    role: div.getAttribute('role') || 'none'
                });
            }

            // 檢查所有有 aria-label 的按鈕
            var buttonsWithAriaLabel = document.querySelectorAll('div[role="button"][aria-label], button[aria-label]');
            for (var i = 0; i < Math.min(buttonsWithAriaLabel.length, 10); i++) {
                var btn = buttonsWithAriaLabel[i];
                info.buttons_with_aria_label.push({
                    aria_label: btn.getAttribute('aria-label'),
                    tag: btn.tagName,
                    role: btn.getAttribute('role') || 'none'
                });
            }

            // 檢查常見的類名
            var allDivs = document.querySelectorAll('div');
            var classCount = {};
            for (var i = 0; i < Math.min(allDivs.length, 100); i++) {
                var div = allDivs[i];
                var className = div.className;
                if (className && className.length > 0) {
                    var firstClass = className.split(' ')[0];
                    classCount[firstClass] = (classCount[firstClass] || 0) + 1;
                }
            }

            // 取前10個最常見的類名
            var sortedClasses = Object.keys(classCount).sort((a, b) => classCount[b] - classCount[a]);
            info.common_classes = sortedClasses.slice(0, 10).map(cls => ({
                class: cls,
                count: classCount[cls]
            }));

            return info;
            """)

            logger.info(f"📊 調試結果：")
            logger.info(f"   - [role=\"article\"] 元素數量: {debug_info.get('articles', 0)}")

            logger.info(f"   - 前10個有 aria-label 的 div:")
            for item in debug_info.get('divs_with_aria_label', []):
                logger.info(f"     * {item['tag']}[role=\"{item['role']}\"] aria-label=\"{item['aria_label'][:50]}...\"")

            logger.info(f"   - 前10個有 aria-label 的按鈕:")
            for item in debug_info.get('buttons_with_aria_label', []):
                logger.info(f"     * {item['tag']}[role=\"{item['role']}\"] aria-label=\"{item['aria_label'][:50]}...\"")

            logger.info(f"   - 前10個最常見的類名:")
            for item in debug_info.get('common_classes', []):
                logger.info(f"     * .{item['class']} (出現 {item['count']} 次)")

        except Exception as e:
            logger.error(f"調試頁面元素失敗: {e}")

    def _debug_all_buttons(self):
        """調試：搜尋所有可能的按鈕"""
        try:
            logger.info("🔍 調試：搜尋所有可能的按鈕...")

            button_info = self.page.run_js("""
            console.log('🔍 搜尋所有按鈕...');

            var allButtons = [];

            // 搜尋所有可能的按鈕元素
            var buttonSelectors = [
                'button',
                'div[role="button"]',
                'span[role="button"]',
                'a[role="button"]',
                '[aria-haspopup]',
                '[onclick]'
            ];

            for (var i = 0; i < buttonSelectors.length; i++) {
                var elements = document.querySelectorAll(buttonSelectors[i]);
                console.log('選擇器:', buttonSelectors[i], '找到', elements.length, '個元素');

                for (var j = 0; j < Math.min(elements.length, 20); j++) {
                    var el = elements[j];
                    var text = (el.textContent || el.innerText || '').trim();
                    var ariaLabel = el.getAttribute('aria-label') || '';
                    var title = el.getAttribute('title') || '';

                    // 檢查是否可見
                    if (el.offsetParent === null) continue;

                    // 只記錄有意義的按鈕
                    if (text.length > 0 || ariaLabel.length > 0 || title.length > 0) {
                        allButtons.push({
                            selector: buttonSelectors[i],
                            text: text.substring(0, 50),
                            ariaLabel: ariaLabel.substring(0, 50),
                            title: title.substring(0, 50),
                            tagName: el.tagName
                        });
                    }
                }
            }

            return allButtons;
            """)

            logger.info(f"📊 找到 {len(button_info)} 個可見按鈕：")
            for i, btn in enumerate(button_info[:15]):  # 只顯示前15個
                logger.info(f"   {i+1}. {btn['tagName']}[{btn['selector']}]")
                if btn['text']:
                    logger.info(f"      文字: \"{btn['text']}\"")
                if btn['ariaLabel']:
                    logger.info(f"      aria-label: \"{btn['ariaLabel']}\"")
                if btn['title']:
                    logger.info(f"      title: \"{btn['title']}\"")
                logger.info("")

        except Exception as e:
            logger.error(f"調試所有按鈕失敗: {e}")

    def _delete_single_post_original(self, three_dots_element) -> bool:
        """使用備份檔案中成功驗證的刪除邏輯"""
        try:
            logger.info("� 準備點擊三個點選單...")

            # 步驟1: 確保元素在視野中並可點擊
            logger.info("📍 確保三個點按鈕在視野中...")

            # 多次嘗試滾動到正確位置
            for attempt in range(3):
                self.page.run_js("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", three_dots_element)
                time.sleep(1.5)

                # 檢查元素是否可見和可點擊
                button_status = self.page.run_js("""
                var element = arguments[0];
                var rect = element.getBoundingClientRect();
                var isVisible = rect.top >= 50 && rect.top <= window.innerHeight - 50 &&
                               rect.left >= 0 && rect.right <= window.innerWidth &&
                               element.offsetParent !== null;

                return {
                    visible: isVisible,
                    top: rect.top,
                    left: rect.left,
                    inViewport: rect.top >= 0 && rect.top <= window.innerHeight
                };
                """, three_dots_element)

                logger.info(f"   嘗試 {attempt + 1}: 按鈕位置 {button_status['top']:.0f}px, 可見: {'✅' if button_status['visible'] else '❌'}")

                if button_status['visible']:
                    logger.info("✅ 按鈕已在可點擊位置")
                    break
                elif button_status['inViewport']:
                    # 按鈕在視野中但位置不佳，微調滾動
                    if button_status['top'] < 100:
                        self.page.run_js("window.scrollBy(0, -100);")
                    elif button_status['top'] > 600:
                        self.page.run_js("window.scrollBy(0, 100);")
                    time.sleep(1)

            # 最終檢查
            final_check = self.page.run_js("""
            var element = arguments[0];
            var rect = element.getBoundingClientRect();
            return rect.top >= 50 && rect.top <= window.innerHeight - 50;
            """, three_dots_element)

            if not final_check:
                logger.warning("⚠️ 三個點按鈕最終仍不在可點擊位置")
                return False

            # 步驟2: 點擊三個點選單
            logger.info("🔘 點擊三個點選單...")
            self.page.run_js("arguments[0].click();", three_dots_element)

            time.sleep(2)  # 等待選單完全展開

            # 步驟3: 搜尋並點擊刪除選項
            delete_result = self.page.run_js("""
            console.log('🔍 搜尋刪除選項...');

            var selectors = [
                'div[role="menuitem"]',
                'div[role="button"]',
                'span[role="menuitem"]',
                'a[role="menuitem"]',
                'div[data-testid*="delete"]',
                'div[tabindex="0"]',
                'div[class*="x1i10hfl"]',
                'div',
                'span'
            ];

            var keywords = [
                '刪除貼文',
                '刪除',
                'Delete post',
                'Delete',
                '删除',
                'Remove',
                '移除',
                '刪除這則貼文',
                'Delete this post'
            ];

            for (var i = 0; i < selectors.length; i++) {
                var elements = document.querySelectorAll(selectors[i]);
                console.log('檢查選擇器:', selectors[i], '找到', elements.length, '個元素');

                for (var j = 0; j < elements.length; j++) {
                    var el = elements[j];
                    var text = el.textContent || el.innerText || '';

                    // 檢查元素是否可見
                    if (el.offsetParent === null) continue;

                    // 檢查是否包含刪除關鍵字
                    for (var k = 0; k < keywords.length; k++) {
                        if (text.includes(keywords[k])) {
                            console.log('✅ 找到刪除選項:', text, '選擇器:', selectors[i]);
                            el.click();
                            return true;
                        }
                    }
                }
            }

            console.log('❌ 未找到刪除選項');
            return false;
            """)

            if not delete_result:
                logger.warning("找不到刪除選項")
                # 嘗試按ESC關閉選單
                self.page.run_js("document.dispatchEvent(new KeyboardEvent('keydown', {key: 'Escape'}));")
                return False

            # 等待確認對話框出現，增加等待時間
            time.sleep(3)

            # 步驟4: 多次嘗試搜尋並點擊確認按鈕
            confirm_result = False
            for confirm_attempt in range(5):  # 最多嘗試5次
                logger.info(f"🔍 第 {confirm_attempt + 1} 次嘗試搜尋確認按鈕...")

                confirm_result = self.page.run_js("""
                console.log('🔍 搜尋確認刪除按鈕...');

                var confirmSelectors = [
                    'div[role="button"]',
                    'button',
                    'div[aria-label*="刪除"]',
                    'button[aria-label*="刪除"]',
                    'div[data-testid*="confirm"]',
                    'div[tabindex="0"]'
                ];

                var confirmKeywords = ['刪除', 'Delete', '确定', 'Confirm', '確認', 'OK', '删除'];

                for (var i = 0; i < confirmSelectors.length; i++) {
                    var elements = document.querySelectorAll(confirmSelectors[i]);
                    console.log('檢查確認選擇器:', confirmSelectors[i], '找到', elements.length, '個元素');

                    for (var j = 0; j < elements.length; j++) {
                        var el = elements[j];
                        var text = (el.textContent || el.innerText || '').trim();

                        // 檢查元素是否可見
                        if (el.offsetParent === null) continue;

                        // 檢查元素是否在視窗內
                        var rect = el.getBoundingClientRect();
                        if (rect.width === 0 || rect.height === 0) continue;

                        // 檢查是否包含確認關鍵字（精確匹配）
                        for (var k = 0; k < confirmKeywords.length; k++) {
                            if (text === confirmKeywords[k] || text.includes(confirmKeywords[k])) {
                                console.log('✅ 找到確認按鈕:', text, '選擇器:', confirmSelectors[i]);

                                // 滾動到元素位置
                                el.scrollIntoView({behavior: 'smooth', block: 'center'});

                                // 等待一下再點擊
                                setTimeout(function() {
                                    el.click();
                                }, 200);

                                return true;
                            }
                        }
                    }
                }

                console.log('❌ 未找到確認按鈕');
                return false;
                """)

                if confirm_result:
                    logger.info("✅ 成功點擊確認按鈕！")
                    break
                else:
                    logger.warning(f"❌ 第 {confirm_attempt + 1} 次嘗試失敗，等待後重試...")
                    time.sleep(1.5)  # 等待後重試

            if not confirm_result:
                logger.warning("找不到確認刪除按鈕，但可能已經刪除成功")
                return True  # 有些情況下不需要確認就直接刪除

            logger.info("✅ 成功完成刪除流程")
            return True

        except Exception as e:
            logger.error(f"刪除單個貼文失敗: {e}")
            return False

def main():
    """主函數"""

    # ==================== 配置區域 ====================

    # 網址清單文件路徑 - 使用臨時目錄
    URLS_FILE_PATH = os.path.join(tempfile.gettempdir(), "urls.txt")  # 網址清單文件名

    # ⚠️ 全部刪除模式 - 不需要設定標籤
    # 此模式會刪除所有可見的貼文，不進行任何篩選
    # 如果需要針對性刪除，請使用網頁介面或修改程式碼調用 delete_posts_with_tags 方法

    # 以下是針對性刪除的標籤設定範例（目前未使用）：
    # TARGET_TAGS = ["🍎銅鑼三房美廈＋平面車位", "❗️小家庭、首購選族❗️"]
    # TARGET_TAGS = ["新的關鍵字1", "新的關鍵字2"]
    # TARGET_TAGS = ["#苑裡市區建地", "#都市計畫內"]  # hashtag格式
    # TARGET_TAGS = ["單一關鍵字"]  # 只有一個關鍵字

    # 是否使用無頭模式（True=不顯示瀏覽器視窗，False=顯示瀏覽器視窗）
    # 注意：使用現有瀏覽器時，無頭模式設定可能無效
    HEADLESS_MODE = False

    # 是否使用現有瀏覽器（True=連接已開啟的瀏覽器，False=創建新瀏覽器）
    USE_EXISTING_BROWSER = True

    # 滾動和載入參數（這些參數在當前版本中已整合到代碼中）
    # MAX_SCROLL_ATTEMPTS = 10      # 最多滾動次數（已整合到滾動邏輯中）
    # SCROLL_WAIT_TIME = 3          # 每次滾動後的等待時間（已整合到滾動邏輯中）
    # DELETE_WAIT_TIME = 6          # 刪除後的等待時間（已整合到刪除邏輯中）
    # LOADING_CHECK_TIMEOUT = 8     # 載入指示器檢查超時（已整合到載入邏輯中）

    # ==================== 執行區域 ====================

    deleter = FacebookDrissionDeleter(headless=HEADLESS_MODE)

    try:
        print("🚀 Facebook 全部貼文刪除器啟動中...")

        # 設定瀏覽器（嘗試連接現有瀏覽器）
        if not deleter.setup_browser(use_existing_browser=USE_EXISTING_BROWSER):
            logger.error("瀏覽器設定失敗")
            print("❌ 瀏覽器設定失敗，請確保Chrome瀏覽器已安裝")
            return

        # 檢查Facebook登入狀態
        if not deleter.check_facebook_login_status():
            logger.error("Facebook未登入")
            print("❌ Facebook未登入，請先在瀏覽器中手動登入Facebook")
            print("💡 提示：請在Chrome瀏覽器中登入Facebook後再運行此腳本")
            return

        print("✅ Facebook登入狀態確認成功")

        # 從文件載入網址清單
        group_user_urls = deleter.load_urls_from_file(URLS_FILE_PATH)
        if not group_user_urls:
            logger.error("沒有載入到任何網址")
            print(f"❌ 沒有載入到任何網址，請檢查 {URLS_FILE_PATH} 文件")
            return

        print(f"📋 已載入 {len(group_user_urls)} 個社團網址")

        # 執行全部刪除
        print("🗑️ 開始全部刪除模式，將刪除所有可見的貼文...")
        result = deleter.delete_all_posts(group_user_urls)

        if result['success']:
            print(f"\n✅ 刪除完成！")
            print(f"📊 處理社團數: {result['processed_groups']}")
            print(f"🗑️ 刪除貼文數: {result['total_deleted']}")
        else:
            print(f"\n❌ 刪除失敗: {result['message']}")

    except KeyboardInterrupt:
        logger.info("用戶中斷操作")
        print("\n⏹️ 操作已中斷")

    except Exception as e:
        logger.error(f"程式執行失敗: {e}")
        print(f"\n❌ 程式執行失敗: {e}")

    finally:
        # 注意：使用現有瀏覽器時，不要關閉瀏覽器
        if not USE_EXISTING_BROWSER:
            deleter.close()
        else:
            logger.info("使用現有瀏覽器，不關閉瀏覽器實例")
            print("💡 瀏覽器保持開啟狀態")

if __name__ == "__main__":
    main()
