try:
    from .auth_system import auth_system
except ImportError:
    from auth_system import auth_system

def test_anti_abuse():
    """測試防濫用機制"""
    device_id = auth_system.get_device_id()
    print(f"當前設備ID: {device_id}")
    
    # 測試場景1: 正常用戶
    print("\n=== 測試場景1: 正常用戶 ===")
    user1 = "100000389581396"  # 您的Facebook ID
    result1 = auth_system.verify_authorization(user1, device_id)
    print(f"用戶 {user1} 授權結果: {result1['authorized']} - {result1['message']}")
    
    # 如果未授權，嘗試自動綁定
    if not result1['authorized'] and auth_system.is_auto_bind_enabled():
        print("嘗試自動綁定...")
        bind_result = auth_system.auto_bind_device(user1, device_id)
        print(f"自動綁定結果: {bind_result['success']} - {bind_result['message']}")
        
        if bind_result['success']:
            result1 = auth_system.verify_authorization(user1, device_id)
            print(f"綁定後授權結果: {result1['authorized']} - {result1.get('message', '')}")
    
    # 測試場景2: 其他用戶嘗試使用已綁定設備
    print("\n=== 測試場景2: 其他用戶嘗試濫用 ===")
    user2 = "100000123456789"  # 假設的其他Facebook ID
    result2 = auth_system.verify_authorization(user2, device_id)
    print(f"用戶 {user2} 授權結果: {result2['authorized']} - {result2['message']}")
    
    if 'bound_user_id' in result2:
        print(f"設備已綁定到: {result2['bound_user_id']}")
        print(f"當前嘗試用戶: {result2['current_user_id']}")
    
    # 測試場景3: 檢查使用日誌
    print("\n=== 測試場景3: 檢查使用日誌 ===")
    try:
        logs = auth_system.supabase.table('usage_logs').select('*').eq(
            'device_id', device_id
        ).order('timestamp', desc=True).limit(5).execute()
        
        print("最近5條使用記錄:")
        for log in logs.data:
            timestamp = log['timestamp'][:19].replace('T', ' ')
            print(f"  {timestamp} - 用戶:{log['user_id']} - 操作:{log['action']}")
            
    except Exception as e:
        print(f"查詢日誌失敗: {e}")

if __name__ == "__main__":
    print("防濫用機制測試")
    print("=" * 50)
    test_anti_abuse()
